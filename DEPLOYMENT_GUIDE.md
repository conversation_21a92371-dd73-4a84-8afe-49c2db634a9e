# Pebl WhatsApp Integration - Deployment Guide

This guide covers the complete deployment of the Pebl WhatsApp integration, including both the backend service and iOS app updates.

## Overview

The integration consists of:
1. **Backend Service**: Node.js/Express server with WhatsApp Business API integration
2. **iOS App Updates**: Enhanced Pebl app with sync capabilities and user authentication
3. **Database**: MongoDB for storing users, messages, and sync data
4. **Push Notifications**: Apple Push Notification service for real-time sync

## Prerequisites

### Backend Requirements
- Node.js 18+ and npm
- MongoDB database (local or cloud)
- WhatsApp Business API account
- OpenAI API key
- SSL certificate for production webhook
- Apple Developer account (for push notifications)

### iOS App Requirements
- Xcode 15+
- iOS 16+ deployment target
- Apple Developer account
- Push notification entitlements

## Backend Deployment

### 1. Environment Setup

Create production environment file:
```bash
cd backend
cp .env.example .env.production
```

Configure production variables:
```env
NODE_ENV=production
PORT=3000

# Database
MONGODB_URI=mongodb+srv://username:<EMAIL>/pebl

# JWT Secret (generate a strong secret)
JWT_SECRET=your-super-secure-jwt-secret-key

# WhatsApp Business API
WHATSAPP_ACCESS_TOKEN=your-production-access-token
WHATSAPP_PHONE_NUMBER_ID=your-phone-number-id
WHATSAPP_WEBHOOK_VERIFY_TOKEN=your-webhook-verify-token

# OpenAI API
OPENAI_API_KEY=your-openai-api-key

# Push Notifications
APNS_KEY_ID=your-apns-key-id
APNS_TEAM_ID=your-apple-team-id
APNS_BUNDLE_ID=com.yourcompany.pebl
APNS_PRODUCTION=true
```

### 2. WhatsApp Business API Setup

1. **Create WhatsApp Business Account**:
   - Go to [Meta for Developers](https://developers.facebook.com/)
   - Create a new app and add WhatsApp Business API
   - Complete business verification process

2. **Configure Webhook**:
   - Set webhook URL: `https://yourdomain.com/api/whatsapp/webhook`
   - Set verify token (same as `WHATSAPP_WEBHOOK_VERIFY_TOKEN`)
   - Subscribe to `messages` webhook field
   - Test webhook verification

3. **Get API Credentials**:
   - Access token from WhatsApp Business API
   - Phone number ID from your WhatsApp Business number
   - Business account ID

### 3. Production Deployment Options

#### Option A: Cloud Platform (Recommended)

**Heroku Deployment**:
```bash
# Install Heroku CLI
npm install -g heroku

# Login and create app
heroku login
heroku create pebl-backend

# Set environment variables
heroku config:set NODE_ENV=production
heroku config:set MONGODB_URI=your-mongodb-uri
heroku config:set JWT_SECRET=your-jwt-secret
# ... set all other environment variables

# Deploy
git push heroku main
```

**Railway Deployment**:
```bash
# Install Railway CLI
npm install -g @railway/cli

# Login and deploy
railway login
railway init
railway up
```

#### Option B: VPS/Server Deployment

```bash
# Install dependencies
npm ci --production

# Install PM2 for process management
npm install -g pm2

# Start application
pm2 start ecosystem.config.js

# Set up nginx reverse proxy
sudo nginx -t
sudo systemctl reload nginx

# Set up SSL with Let's Encrypt
sudo certbot --nginx -d yourdomain.com
```

### 4. Database Setup

**MongoDB Atlas (Cloud)**:
1. Create MongoDB Atlas account
2. Create new cluster
3. Configure network access (whitelist your server IP)
4. Create database user
5. Get connection string

**Local MongoDB**:
```bash
# Install MongoDB
sudo apt-get install mongodb

# Start MongoDB service
sudo systemctl start mongodb
sudo systemctl enable mongodb
```

### 5. SSL Certificate Setup

For production webhook, you need HTTPS:

**Using Let's Encrypt**:
```bash
sudo apt-get install certbot python3-certbot-nginx
sudo certbot --nginx -d yourdomain.com
```

**Using Cloudflare**:
1. Add your domain to Cloudflare
2. Enable SSL/TLS encryption
3. Set SSL mode to "Full (strict)"

## iOS App Deployment

### 1. Update App Configuration

Update `AppConfig.swift` with production backend URL:
```swift
#if DEBUG
static let backendBaseURL = "http://localhost:3000"
#else
static let backendBaseURL = "https://your-production-domain.com"
#endif
```

### 2. Push Notification Setup

1. **Create APNs Key**:
   - Go to Apple Developer portal
   - Create new APNs key
   - Download the .p8 file
   - Note the Key ID and Team ID

2. **Configure App Capabilities**:
   - Enable Push Notifications in Xcode
   - Add Background App Refresh capability
   - Add Background Processing capability

3. **Upload APNs Key to Backend**:
   - Place .p8 file in backend server
   - Set APNS environment variables

### 3. App Store Deployment

1. **Update Version and Build Numbers**
2. **Archive and Upload**:
   ```bash
   # Archive the app
   xcodebuild archive -project Pebl.xcodeproj -scheme Pebl -archivePath Pebl.xcarchive
   
   # Upload to App Store Connect
   xcodebuild -exportArchive -archivePath Pebl.xcarchive -exportPath . -exportOptionsPlist ExportOptions.plist
   ```

3. **App Store Connect**:
   - Upload build using Xcode or Transporter
   - Fill in app metadata
   - Submit for review

## Testing the Complete Flow

### 1. Backend Health Check
```bash
curl https://yourdomain.com/health
```

### 2. WhatsApp Webhook Test
```bash
curl -X GET "https://yourdomain.com/api/whatsapp/webhook?hub.mode=subscribe&hub.challenge=test&hub.verify_token=your-verify-token"
```

### 3. End-to-End Message Flow
1. Send a message to your WhatsApp Business number
2. Check backend logs for message processing
3. Verify message appears in MongoDB
4. Check if push notification is sent
5. Verify message syncs to iOS app

### 4. User Registration Flow
1. Open iOS app
2. Register with email
3. Link WhatsApp number
4. Send test message
5. Verify categorization and sync

## Monitoring and Maintenance

### 1. Logging
- Backend logs are in `logs/` directory
- Use log aggregation service (e.g., LogDNA, Papertrail)
- Monitor error rates and response times

### 2. Database Monitoring
- Set up MongoDB monitoring
- Configure automated backups
- Monitor storage usage

### 3. WhatsApp API Monitoring
- Monitor webhook delivery success
- Track API rate limits
- Monitor message processing times

### 4. iOS App Analytics
- Implement crash reporting (e.g., Crashlytics)
- Monitor sync success rates
- Track user engagement

## Security Checklist

- [ ] HTTPS enabled for all endpoints
- [ ] JWT secrets are secure and rotated
- [ ] Database access is restricted
- [ ] WhatsApp webhook signature verification enabled
- [ ] Rate limiting configured
- [ ] Input validation implemented
- [ ] Error messages don't leak sensitive information
- [ ] Push notification certificates secured

## Troubleshooting

### Common Issues

1. **Webhook not receiving messages**:
   - Check webhook URL is accessible
   - Verify SSL certificate
   - Check WhatsApp webhook configuration

2. **Push notifications not working**:
   - Verify APNs certificates
   - Check device token registration
   - Ensure production/sandbox environment matches

3. **Database connection issues**:
   - Check MongoDB URI
   - Verify network access
   - Check authentication credentials

4. **Sync not working**:
   - Check user authentication
   - Verify backend API endpoints
   - Check network connectivity

## Support and Maintenance

- Monitor application logs daily
- Update dependencies regularly
- Backup database weekly
- Test webhook functionality monthly
- Review and rotate secrets quarterly

## Rollback Plan

If issues occur:
1. Revert to previous backend version
2. Update iOS app backend URL if needed
3. Restore database from backup if necessary
4. Notify users of any service interruption

This completes the deployment guide for the Pebl WhatsApp integration.
