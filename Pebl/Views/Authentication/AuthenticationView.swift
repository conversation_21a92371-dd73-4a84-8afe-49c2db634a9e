//
//  AuthenticationView.swift
//  Pebl
//
//  Created by AI Assistant on 7/3/25.
//

import SwiftUI

struct AuthenticationView: View {
    @EnvironmentObject var userManager: UserManager
    @State private var email = ""
    @State private var showingWhatsAppInfo = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 30) {
                Spacer()
                
                // App Logo and Title
                VStack(spacing: 16) {
                    Image(systemName: "brain.head.profile")
                        .font(.system(size: 80))
                        .foregroundColor(.blue)
                    
                    Text("Pebl")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                    
                    Text("Your Smart Personal Assistant")
                        .font(.headline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                
                Spacer()
                
                // Features Section
                VStack(alignment: .leading, spacing: 16) {
                    FeatureRow(
                        icon: "brain",
                        title: "AI Categorization",
                        description: "Automatically organize your thoughts and tasks"
                    )
                    
                    FeatureRow(
                        icon: "message.fill",
                        title: "WhatsApp Integration",
                        description: "Send messages via WhatsApp and sync to Pebl"
                    )
                    
                    FeatureRow(
                        icon: "folder.fill",
                        title: "Smart Organization",
                        description: "Create categories and subcategories automatically"
                    )
                    
                    FeatureRow(
                        icon: "hashtag",
                        title: "Hashtag Support",
                        description: "Use #category/subcategory for instant organization"
                    )
                }
                .padding(.horizontal)
                
                Spacer()
                
                // Authentication Section
                VStack(spacing: 16) {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Get Started")
                            .font(.headline)
                        
                        Text("Enter your email to create an account or sign in")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        TextField("<EMAIL>", text: $email)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                            .keyboardType(.emailAddress)
                            .textContentType(.emailAddress)
                            .autocapitalization(.none)
                    }
                    
                    if let error = userManager.authError {
                        Text(error)
                            .foregroundColor(.red)
                            .font(.caption)
                    }
                    
                    Button(action: {
                        userManager.authenticateUser(email: email)
                    }) {
                        HStack {
                            if userManager.isLoading {
                                ProgressView()
                                    .scaleEffect(0.8)
                                    .foregroundColor(.white)
                            }
                            Text(userManager.isLoading ? "Signing In..." : "Continue")
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.blue)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                    }
                    .disabled(email.isEmpty || userManager.isLoading)
                    
                    Button(action: {
                        showingWhatsAppInfo = true
                    }) {
                        Text("Learn about WhatsApp Integration")
                            .font(.caption)
                            .foregroundColor(.blue)
                    }
                }
                .padding(.horizontal)
                
                Spacer()
            }
            .padding()
            .navigationBarHidden(true)
        }
        .sheet(isPresented: $showingWhatsAppInfo) {
            WhatsAppInfoView()
        }
    }
}

// MARK: - Feature Row

struct FeatureRow: View {
    let icon: String
    let title: String
    let description: String
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(.blue)
                .frame(width: 30)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.headline)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
    }
}

// MARK: - WhatsApp Info View

struct WhatsAppInfoView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("WhatsApp Integration")
                            .font(.largeTitle)
                            .fontWeight(.bold)
                        
                        Text("Send messages to Pebl via WhatsApp and have them automatically categorized and organized.")
                            .font(.headline)
                            .foregroundColor(.secondary)
                    }
                    
                    VStack(alignment: .leading, spacing: 16) {
                        Text("How it works:")
                            .font(.title2)
                            .fontWeight(.semibold)
                        
                        StepView(
                            number: 1,
                            title: "Connect Your Number",
                            description: "Link your WhatsApp number to your Pebl account"
                        )
                        
                        StepView(
                            number: 2,
                            title: "Send Messages",
                            description: "Send any message to your Pebl WhatsApp Business number"
                        )
                        
                        StepView(
                            number: 3,
                            title: "Auto-Categorization",
                            description: "Messages are automatically categorized using AI or hashtags"
                        )
                        
                        StepView(
                            number: 4,
                            title: "Sync to App",
                            description: "Messages appear in your Pebl app, organized and ready to use"
                        )
                    }
                    
                    VStack(alignment: .leading, spacing: 12) {
                        Text("Examples:")
                            .font(.title2)
                            .fontWeight(.semibold)
                        
                        ExampleMessageView(
                            message: "Buy groceries: milk, bread, eggs",
                            category: "Shopping"
                        )
                        
                        ExampleMessageView(
                            message: "#work/meetings Schedule team sync for Friday",
                            category: "Work > Meetings"
                        )
                        
                        ExampleMessageView(
                            message: "Remember to call mom this weekend",
                            category: "Personal"
                        )
                    }
                    
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Privacy & Security")
                            .font(.title2)
                            .fontWeight(.semibold)
                        
                        Text("• Your messages are processed securely")
                        Text("• Only you can see your organized content")
                        Text("• WhatsApp integration can be disconnected anytime")
                        Text("• No message content is stored permanently on our servers")
                    }
                    .font(.body)
                    .foregroundColor(.secondary)
                }
                .padding()
            }
            .navigationTitle("WhatsApp Integration")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

// MARK: - Step View

struct StepView: View {
    let number: Int
    let title: String
    let description: String
    
    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            Text("\(number)")
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(.white)
                .frame(width: 24, height: 24)
                .background(Color.blue)
                .clipShape(Circle())
            
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.headline)
                
                Text(description)
                    .font(.body)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
    }
}

// MARK: - Example Message View

struct ExampleMessageView: View {
    let message: String
    let category: String
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: "message.fill")
                    .foregroundColor(.green)
                Text(message)
                    .font(.body)
                Spacer()
            }
            .padding()
            .background(Color.green.opacity(0.1))
            .cornerRadius(8)
            
            HStack {
                Image(systemName: "arrow.right")
                    .foregroundColor(.blue)
                Text("Categorized as: \(category)")
                    .font(.caption)
                    .foregroundColor(.blue)
                Spacer()
            }
            .padding(.leading)
        }
    }
}

#Preview {
    AuthenticationView()
        .environmentObject(UserManager.shared)
}
