//
//  CategoryBanner.swift
//  Pebl
//
//  Created by AI Assistant on 7/28/25.
//

import SwiftUI

/// Banner that shows which category a message was added to
struct CategoryBanner: View {
    let categoryName: String
    let subcategoryName: String?
    let isNewCategory: <PERSON><PERSON>
    @Binding var isVisible: Bool
    
    var body: some View {
        VStack {
            if isVisible {
                HStack(spacing: 12) {
                    // Icon
                    Image(systemName: isNewCategory ? "folder.badge.plus" : "checkmark.circle.fill")
                        .foregroundColor(isNewCategory ? .orange : .green)
                        .font(.system(size: 16, weight: .medium))
                    
                    // Text content
                    VStack(alignment: .leading, spacing: 2) {
                        Text(bannerTitle)
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(.primary)
                        
                        Text(bannerSubtitle)
                            .font(.system(size: 12))
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                    
                    // Dismiss button
                    Button(action: {
                        withAnimation(.easeInOut(duration: 0.3)) {
                            isVisible = false
                        }
                    }) {
                        Image(systemName: "xmark")
                            .foregroundColor(.secondary)
                            .font(.system(size: 12, weight: .medium))
                    }
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color(.systemBackground))
                        .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
                )
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(bannerBorderColor, lineWidth: 1)
                )
                .padding(.horizontal, 16)
                .transition(.move(edge: .top).combined(with: .opacity))
            }
            
            Spacer()
        }
    }
    
    private var bannerTitle: String {
        if isNewCategory {
            return "Created new category"
        } else {
            return "Message added"
        }
    }
    
    private var bannerSubtitle: String {
        if let subcategoryName = subcategoryName {
            return "Added to \(categoryName) → \(subcategoryName)"
        } else {
            return "Added to \(categoryName)"
        }
    }
    
    private var bannerBorderColor: Color {
        if isNewCategory {
            return .orange.opacity(0.3)
        } else {
            return .green.opacity(0.3)
        }
    }
}

/// Banner information for showing categorization results
struct CategoryBannerInfo {
    let categoryName: String
    let subcategoryName: String?
    let isNewCategory: Bool
    
    init(categoryName: String, subcategoryName: String? = nil, isNewCategory: Bool = false) {
        self.categoryName = categoryName
        self.subcategoryName = subcategoryName
        self.isNewCategory = isNewCategory
    }
}

#Preview {
    VStack(spacing: 20) {
        // New category example
        CategoryBanner(
            categoryName: "Shopping",
            subcategoryName: nil,
            isNewCategory: true,
            isVisible: .constant(true)
        )
        
        // Existing category example
        CategoryBanner(
            categoryName: "Movies to Watch",
            subcategoryName: nil,
            isNewCategory: false,
            isVisible: .constant(true)
        )
        
        // Subcategory example
        CategoryBanner(
            categoryName: "Movies to Watch",
            subcategoryName: "Comedy",
            isNewCategory: false,
            isVisible: .constant(true)
        )
        
        Spacer()
    }
    .padding()
    .background(Color(.systemGroupedBackground))
}
