const express = require('express');
const jwt = require('jsonwebtoken');
const User = require('../models/User');
const logger = require('../utils/logger');

const router = express.Router();

// Register or login user (simplified for demo)
router.post('/register', async (req, res) => {
  try {
    const { email, phoneNumber, deviceToken, platform = 'ios' } = req.body;

    // Support both email and phone number registration
    if (!email && !phoneNumber) {
      return res.status(400).json({
        error: 'Either email or phone number is required'
      });
    }

    // Find existing user by email or phone number
    let user;
    if (email) {
      user = await User.findOne({ email: email.toLowerCase() });
    } else if (phoneNumber) {
      user = await User.findOne({ whatsappNumber: phoneNumber });
    }

    if (!user) {
      const userData = {};
      if (email) userData.email = email.toLowerCase();
      if (phoneNumber) userData.whatsappNumber = phoneNumber;

      user = new User(userData);
      await user.save();
      logger.info('New user created', { userId: user._id, email: email || 'none', phoneNumber: phoneNumber || 'none' });
    }

    // Add device token if provided
    if (deviceToken) {
      await user.addDeviceToken(deviceToken, platform);
      logger.info('Device token added', { userId: user._id });
    }

    // Generate JWT token
    const token = jwt.sign(
      {
        userId: user._id,
        email: user.email || null,
        phoneNumber: user.whatsappNumber || null
      },
      process.env.JWT_SECRET,
      { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
    );

    res.status(200).json({
      success: true,
      user: {
        id: user._id,
        email: user.email,
        whatsappNumber: user.whatsappNumber,
        isWhatsappVerified: user.isWhatsappVerified,
        preferences: user.preferences
      },
      token
    });

  } catch (error) {
    logger.error('Error in auth/register:', error);
    res.status(500).json({
      error: 'Registration failed',
      details: error.message
    });
  }
});

// Update device token
router.post('/device-token', async (req, res) => {
  try {
    const { userId, deviceToken, platform = 'ios' } = req.body;
    
    if (!userId || !deviceToken) {
      return res.status(400).json({
        error: 'Missing required fields: userId, deviceToken'
      });
    }

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        error: 'User not found'
      });
    }

    await user.addDeviceToken(deviceToken, platform);

    res.status(200).json({
      success: true,
      message: 'Device token updated'
    });

  } catch (error) {
    logger.error('Error updating device token:', error);
    res.status(500).json({
      error: 'Failed to update device token',
      details: error.message
    });
  }
});

// Remove device token
router.delete('/device-token', async (req, res) => {
  try {
    const { userId, deviceToken } = req.body;
    
    if (!userId || !deviceToken) {
      return res.status(400).json({
        error: 'Missing required fields: userId, deviceToken'
      });
    }

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        error: 'User not found'
      });
    }

    await user.removeDeviceToken(deviceToken);

    res.status(200).json({
      success: true,
      message: 'Device token removed'
    });

  } catch (error) {
    logger.error('Error removing device token:', error);
    res.status(500).json({
      error: 'Failed to remove device token',
      details: error.message
    });
  }
});

// Verify JWT token
router.post('/verify', async (req, res) => {
  try {
    const { token } = req.body;
    
    if (!token) {
      return res.status(400).json({
        error: 'Token is required'
      });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const user = await User.findById(decoded.userId);
    
    if (!user || !user.isActive) {
      return res.status(401).json({
        error: 'Invalid token or user not found'
      });
    }

    res.status(200).json({
      success: true,
      user: {
        id: user._id,
        email: user.email,
        whatsappNumber: user.whatsappNumber,
        isWhatsappVerified: user.isWhatsappVerified,
        preferences: user.preferences
      }
    });

  } catch (error) {
    logger.error('Error verifying token:', error);
    res.status(401).json({
      error: 'Invalid token',
      details: error.message
    });
  }
});

module.exports = router;
