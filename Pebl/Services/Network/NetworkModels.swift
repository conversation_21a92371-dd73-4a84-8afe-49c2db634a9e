//
//  NetworkModels.swift
//  Pebl
//
//  Created by AI Assistant on 7/3/25.
//

import Foundation

// MARK: - Request Models

struct RegisterRequest: Codable {
    let email: String
    let deviceToken: String?
    let platform: String = "ios"
}

struct RegisterPhoneRequest: Codable {
    let phoneNumber: String
    let deviceToken: String?
    let platform: String = "ios"
}

struct DeviceTokenRequest: Codable {
    let userId: String
    let deviceToken: String
    let platform: String = "ios"
}

struct LinkAccountRequest: Codable {
    let userId: String
    let whatsappNumber: String
}

struct VerifyNumberRequest: Codable {
    let userId: String
    let verificationCode: String
}

struct MarkSyncedRequest: Codable {
    let messageIds: [String]
}

struct UpdatePreferencesRequest: Codable {
    let preferences: UserPreferences
}

// MARK: - Response Models

struct BasicResponse: Codable {
    let success: Bool
    let message: String?
}

struct AuthResponse: Codable {
    let success: Bool
    let user: BackendUser
    let token: String
}

struct LinkAccountResponse: Codable {
    let success: Bool
    let verificationCode: String
}

struct VerifyNumberResponse: Codable {
    let success: Bool
    let message: String
}

struct WhatsAppStatusResponse: Codable {
    let hasWhatsappNumber: Bool
    let isVerified: Bool
    let whatsappNumber: String?
    let enableSync: Bool
}

struct MessagesResponse: Codable {
    let success: Bool
    let messages: [BackendMessage]
    let count: Int
}

struct MessageStatisticsResponse: Codable {
    let success: Bool
    let statistics: MessageStatistics
}

// MARK: - Data Models

struct BackendUser: Codable {
    let id: String
    let email: String
    let whatsappNumber: String?
    let isWhatsappVerified: Bool
    var preferences: UserPreferences
    let lastCategorySync: Date?
    let createdAt: Date
}

struct BackendMessage: Codable {
    let id: String
    let text: String
    let mainMessage: String
    let subcontent: String
    let source: MessageSource
    let whatsappMessageId: String?
    let whatsappTimestamp: Date?
    let userId: String
    let categoryName: String
    let subcategoryName: String?
    let aiCategorization: AICategorization?
    let isCompleted: Bool
    let syncStatus: SyncStatus
    let timestamp: Date
    let createdAt: Date
    
    enum CodingKeys: String, CodingKey {
        case id = "_id"
        case text, mainMessage, subcontent, source
        case whatsappMessageId, whatsappTimestamp, userId
        case categoryName, subcategoryName, aiCategorization
        case isCompleted, syncStatus, timestamp, createdAt
    }
}

struct AICategorization: Codable {
    let confidence: Double
    let suggestedCategories: [String]
    let processingTime: Int
    let model: String
}

struct UserPreferences: Codable {
    let messageExpirationDays: Int
    let autoDeleteExpiredMessages: Bool
    let enableWhatsappSync: Bool
    let notificationSettings: NotificationSettings
}

struct NotificationSettings: Codable {
    let pushNotifications: Bool
    let whatsappMessages: Bool
}

struct MessageStatistics: Codable {
    let overview: MessageOverview
    let categories: [CategoryStatistic]
}

struct MessageOverview: Codable {
    let totalMessages: Int
    let whatsappMessages: Int
    let appMessages: Int
    let completedMessages: Int
    let pendingSyncMessages: Int
}

struct CategoryStatistic: Codable {
    let id: String
    let count: Int
    let lastMessage: Date
    
    enum CodingKeys: String, CodingKey {
        case id = "_id"
        case count, lastMessage
    }
}

// MARK: - Enums

enum MessageSource: String, Codable {
    case app = "app"
    case whatsapp = "whatsapp"
}

enum SyncStatus: String, Codable {
    case pending = "pending"
    case synced = "synced"
    case failed = "failed"
}

// MARK: - Extensions

extension BackendMessage {
    /// Convert backend message to local Message model
    func toLocalMessage() -> Message {
        return Message(
            text: self.text,
            isCompleted: self.isCompleted,
            categoryName: self.categoryName,
            timestamp: self.timestamp
        )
    }
}

extension UserPreferences {
    /// Convert to local AppSettings
    func toAppSettings() -> AppSettings {
        var settings = AppSettings()
        settings.messageExpirationDays = self.messageExpirationDays
        settings.autoDeleteExpiredMessages = self.autoDeleteExpiredMessages
        return settings
    }
    
    /// Create from local AppSettings
    static func from(appSettings: AppSettings, whatsappEnabled: Bool = true) -> UserPreferences {
        return UserPreferences(
            messageExpirationDays: appSettings.messageExpirationDays,
            autoDeleteExpiredMessages: appSettings.autoDeleteExpiredMessages,
            enableWhatsappSync: whatsappEnabled,
            notificationSettings: NotificationSettings(
                pushNotifications: true,
                whatsappMessages: true
            )
        )
    }
}
