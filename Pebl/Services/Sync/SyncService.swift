//
//  SyncService.swift
//  Pebl
//
//  Created by AI Assistant on 7/3/25.
//

import Foundation
import Combine
import UIKit

/// Service responsible for syncing messages between backend and local storage
class SyncService: ObservableObject {
    
    // MARK: - Singleton
    
    static let shared = SyncService()
    
    private init() {}
    
    // MARK: - Properties
    
    @Published var isSyncing = false
    @Published var lastSyncDate: Date?
    @Published var syncError: String?

    private let networkService = NetworkService.shared
    private var cancellables = Set<AnyCancellable>()
    private var currentUserId: String?
    
    // MARK: - Public Methods
    
    /// Sync pending messages from backend to local storage
    func syncPendingMessages(for userId: String, categoryManager: CategoryManager, completion: (() -> Void)? = nil) {
        guard !isSyncing else {
            print("🔄 Sync already in progress")
            return
        }
        
        isSyncing = true
        syncError = nil
        
        print("🔄 Starting message sync for user: \(userId)")
        
        networkService.getPendingSyncMessages(userId: userId)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] syncCompletion in
                    self?.isSyncing = false

                    switch syncCompletion {
                    case .finished:
                        self?.lastSyncDate = Date()
                        print("✅ Message sync completed successfully")
                    case .failure(let error):
                        self?.syncError = error.localizedDescription
                        print("❌ Message sync failed: \(error.localizedDescription)")
                    }

                    // Call completion handler
                    completion?()
                },
                receiveValue: { [weak self] response in
                    self?.processSyncedMessages(response.messages, categoryManager: categoryManager, userId: userId)
                }
            )
            .store(in: &cancellables)
    }
    
    /// Force sync all messages (useful for initial setup)
    func performFullSync(for userId: String, categoryManager: CategoryManager) {
        guard !isSyncing else { return }
        
        isSyncing = true
        syncError = nil
        
        print("🔄 Starting full message sync for user: \(userId)")
        
        networkService.getMessages(userId: userId, limit: 1000)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    self?.isSyncing = false
                    
                    switch completion {
                    case .finished:
                        self?.lastSyncDate = Date()
                        print("✅ Full sync completed successfully")
                    case .failure(let error):
                        self?.syncError = error.localizedDescription
                        print("❌ Full sync failed: \(error.localizedDescription)")
                    }
                },
                receiveValue: { [weak self] response in
                    self?.processSyncedMessages(response.messages, categoryManager: categoryManager, userId: userId, isFullSync: true)
                }
            )
            .store(in: &cancellables)
    }
    
    /// Mark messages as synced on the backend
    func markMessagesSynced(messageIds: [String]) {
        guard !messageIds.isEmpty else { return }
        
        networkService.markMessagesSynced(messageIds: messageIds)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { completion in
                    switch completion {
                    case .finished:
                        print("✅ Marked \(messageIds.count) messages as synced")
                    case .failure(let error):
                        print("❌ Failed to mark messages as synced: \(error.localizedDescription)")
                    }
                },
                receiveValue: { _ in }
            )
            .store(in: &cancellables)
    }
    
    /// Get sync statistics
    func getSyncStatistics(for userId: String) -> AnyPublisher<MessageStatistics, NetworkError> {
        return networkService.getMessageStatistics(userId: userId)
            .map(\.statistics)
            .eraseToAnyPublisher()
    }
    
    // MARK: - Private Methods
    
    private func processSyncedMessages(
        _ backendMessages: [BackendMessage],
        categoryManager: CategoryManager,
        userId: String,
        isFullSync: Bool = false
    ) {
        guard !backendMessages.isEmpty else {
            print("📭 No messages to sync")
            return
        }
        
        print("📥 Processing \(backendMessages.count) synced messages")
        
        var syncedMessageIds: [String] = []
        var newMessagesCount = 0
        
        for backendMessage in backendMessages {
            // Check if message already exists locally (for full sync)
            if isFullSync && messageExistsLocally(backendMessage, in: categoryManager) {
                continue
            }
            
            // Convert backend message to local message
            let localMessage = backendMessage.toLocalMessage()
            
            // Find or create category
            var targetCategory = categoryManager.findCategory(named: backendMessage.categoryName)
            
            if targetCategory == nil {
                // Create new category
                targetCategory = categoryManager.addRootCategory(
                    name: backendMessage.categoryName,
                    sfSymbol: "folder" // Default symbol, could be enhanced with AI
                )
                print("🆕 Created new category: \(backendMessage.categoryName)")
            }
            
            // Add message to category
            if let category = targetCategory {
                // Handle subcategory if specified
                if let subcategoryName = backendMessage.subcategoryName, !subcategoryName.isEmpty {
                    var subcategory = category.findSubcategory(named: subcategoryName)
                    if subcategory == nil {
                        subcategory = category.addSubcategory(name: subcategoryName)
                        print("🆕 Created new subcategory: \(subcategoryName)")
                    }
                    subcategory?.addMessage(localMessage)
                } else {
                    category.addMessage(localMessage)
                }
                
                newMessagesCount += 1
                syncedMessageIds.append(backendMessage.id)
                
                print("✅ Added message to \(backendMessage.categoryName): \(localMessage.mainMessage)")
            }
        }
        
        // Save changes to local storage
        categoryManager.saveToFile()
        
        // Mark messages as synced on backend
        if !syncedMessageIds.isEmpty {
            markMessagesSynced(messageIds: syncedMessageIds)
        }
        
        print("🎉 Sync completed: \(newMessagesCount) new messages added")
    }
    
    private func messageExistsLocally(_ backendMessage: BackendMessage, in categoryManager: CategoryManager) -> Bool {
        // Check if a message with the same text and timestamp already exists
        // This is a simple check - could be enhanced with better deduplication logic
        
        guard let category = categoryManager.findCategory(named: backendMessage.categoryName) else {
            return false
        }
        
        let existingMessages = category.messages + category.subcategories.flatMap { $0.messages }
        
        return existingMessages.contains { localMessage in
            localMessage.text == backendMessage.text &&
            abs(localMessage.timestamp.timeIntervalSince(backendMessage.timestamp)) < 60 // Within 1 minute
        }
    }
}

// MARK: - Sync Configuration

extension SyncService {
    
    /// Configure automatic sync intervals
    func configureAutoSync(for userId: String, categoryManager: CategoryManager) {
        self.currentUserId = userId

        // Set up periodic sync every 5 minutes when app is active
        Timer.publish(every: 300, on: .main, in: .common)
            .autoconnect()
            .sink { [weak self] _ in
                self?.syncPendingMessages(for: userId, categoryManager: categoryManager)
            }
            .store(in: &cancellables)

        // Setup background sync
        setupBackgroundSync()

        print("🔄 Auto sync configured for user: \(userId)")
    }

    /// Setup background app refresh for sync
    private func setupBackgroundSync() {
        // Listen for app lifecycle events
        NotificationCenter.default.addObserver(
            forName: UIApplication.didEnterBackgroundNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            self?.handleAppDidEnterBackground()
        }

        NotificationCenter.default.addObserver(
            forName: UIApplication.willEnterForegroundNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            self?.handleAppWillEnterForeground()
        }
    }

    /// Handle app entering background
    private func handleAppDidEnterBackground() {
        print("📱 App entered background - scheduling background sync")

        // Schedule background task
        let backgroundTaskId = UIApplication.shared.beginBackgroundTask(withName: "MessageSync") {
            // Background task expired
            print("⏰ Background sync task expired")
        }

        // Perform quick sync if needed
        if let userId = currentUserId {
            syncPendingMessages(for: userId, categoryManager: CategoryManager.shared) { [weak self] in
                UIApplication.shared.endBackgroundTask(backgroundTaskId)
            }
        } else {
            UIApplication.shared.endBackgroundTask(backgroundTaskId)
        }
    }

    /// Handle app entering foreground
    private func handleAppWillEnterForeground() {
        print("📱 App entering foreground - triggering sync")

        // Trigger immediate sync when app becomes active
        if let userId = currentUserId {
            syncPendingMessages(for: userId, categoryManager: CategoryManager.shared)
        }
    }
    
    /// Handle app becoming active (sync immediately)
    func handleAppBecameActive(for userId: String, categoryManager: CategoryManager) {
        // Check if we need to sync based on time since last sync
        let timeSinceLastSync = Date().timeIntervalSince(lastSyncDate ?? Date.distantPast)

        if timeSinceLastSync > AppConfig.Sync.forceRefreshInterval {
            print("🔄 Force refresh - last sync was \(Int(timeSinceLastSync)) seconds ago")
            syncPendingMessages(for: userId, categoryManager: categoryManager)
        } else {
            print("⏭️ Skipping sync - last sync was \(Int(timeSinceLastSync)) seconds ago")
        }
    }
}

// MARK: - Sync Status

enum SyncState {
    case idle
    case syncing
    case success(Date)
    case error(String)
    
    var isActive: Bool {
        switch self {
        case .syncing:
            return true
        default:
            return false
        }
    }
}
