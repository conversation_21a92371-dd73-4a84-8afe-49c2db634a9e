//
//  UserManager.swift
//  Pebl
//
//  Created by AI Assistant on 7/3/25.
//

import Foundation
import Combine
import UserNotifications
import UIKit

/// Manages user authentication and backend integration
class UserManager: ObservableObject {
    
    // MARK: - Singleton
    
    static let shared = UserManager()
    
    private init() {
        // App works independently by default
        // Authentication is optional for WhatsApp integration
        loadUserFromStorage()
        setupNotifications()
    }
    
    // MARK: - Published Properties
    
    @Published var currentUser: BackendUser?
    @Published var isAuthenticated = false
    @Published var authToken: String?
    @Published var isLoading = false
    @Published var authError: String?
    
    // MARK: - Private Properties
    
    private let networkService = NetworkService.shared
    private let syncService = SyncService.shared
    private let whatsappService = WhatsAppIntegrationService.shared
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Authentication
    
    /// Register or login user with email
    func authenticateUser(email: String) {
        guard !email.isEmpty else {
            authError = "Email cannot be empty"
            return
        }
        
        isLoading = true
        authError = nil
        
        // Get device token for push notifications
        getDeviceToken { [weak self] deviceToken in
            self?.performAuthentication(email: email, deviceToken: deviceToken)
        }
    }
    
    private func performAuthentication(email: String, deviceToken: String?) {
        networkService.registerUser(email: email, deviceToken: deviceToken)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    self?.isLoading = false
                    
                    switch completion {
                    case .finished:
                        print("✅ User authentication completed")
                    case .failure(let error):
                        self?.authError = error.localizedDescription
                        print("❌ Authentication failed: \(error.localizedDescription)")
                    }
                },
                receiveValue: { [weak self] response in
                    if response.success {
                        self?.handleSuccessfulAuth(user: response.user, token: response.token)
                    } else {
                        self?.authError = "Authentication failed"
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    private func handleSuccessfulAuth(user: BackendUser, token: String) {
        currentUser = user
        authToken = token
        isAuthenticated = true
        
        // Save to storage
        saveUserToStorage()
        
        // Initialize services
        initializeServices()
        
        print("🎉 User authenticated successfully: \(user.email)")
    }
    
    /// Logout user
    func logout() {
        currentUser = nil
        authToken = nil
        isAuthenticated = false
        
        // Clear storage
        clearUserFromStorage()
        
        // Reset services
        whatsappService.disconnectWhatsApp()
        
        print("👋 User logged out")
    }
    
    // MARK: - Device Token Management
    
    private func getDeviceToken(completion: @escaping (String?) -> Void) {
        UNUserNotificationCenter.current().requestAuthorization(options: [.alert, .badge, .sound]) { granted, error in
            if granted {
                DispatchQueue.main.async {
                    UIApplication.shared.registerForRemoteNotifications()
                }
            }
            
            // For now, return nil - device token will be set later via delegate
            completion(nil)
        }
    }
    
    /// Update device token when received from AppDelegate
    func updateDeviceToken(_ deviceToken: String) {
        guard let userId = currentUser?.id else { return }
        
        networkService.updateDeviceToken(userId: userId, deviceToken: deviceToken)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { completion in
                    switch completion {
                    case .finished:
                        print("✅ Device token updated successfully")
                    case .failure(let error):
                        print("❌ Failed to update device token: \(error.localizedDescription)")
                    }
                },
                receiveValue: { _ in }
            )
            .store(in: &cancellables)
    }
    
    // MARK: - Service Initialization
    
    private func initializeServices() {
        guard let userId = currentUser?.id else { return }
        
        // Initialize WhatsApp service
        whatsappService.initialize(for: userId)
        
        // Configure auto sync
        syncService.configureAutoSync(for: userId, categoryManager: CategoryManager.shared)
        
        // Perform initial sync
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            self.syncService.syncPendingMessages(for: userId, categoryManager: CategoryManager.shared)
        }
    }
    
    // MARK: - User Preferences
    
    /// Update user preferences on backend
    func updatePreferences(_ preferences: UserPreferences) {
        guard let userId = currentUser?.id else { return }
        
        networkService.updateUserPreferences(userId: userId, preferences: preferences)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { completion in
                    switch completion {
                    case .finished:
                        print("✅ User preferences updated")
                    case .failure(let error):
                        print("❌ Failed to update preferences: \(error.localizedDescription)")
                    }
                },
                receiveValue: { [weak self] _ in
                    // Update local user object
                    self?.currentUser?.preferences = preferences
                    self?.saveUserToStorage()
                }
            )
            .store(in: &cancellables)
    }
    
    // MARK: - Storage Management
    
    private func saveUserToStorage() {
        guard let user = currentUser, let token = authToken else { return }
        
        do {
            let userData = try JSONEncoder().encode(user)
            UserDefaults.standard.set(userData, forKey: "current_user")
            UserDefaults.standard.set(token, forKey: "auth_token")
            UserDefaults.standard.set(true, forKey: "is_authenticated")
        } catch {
            print("❌ Failed to save user to storage: \(error)")
        }
    }
    
    private func loadUserFromStorage() {
        guard let userData = UserDefaults.standard.data(forKey: "current_user"),
              let token = UserDefaults.standard.string(forKey: "auth_token"),
              UserDefaults.standard.bool(forKey: "is_authenticated") else {
            return
        }
        
        do {
            let user = try JSONDecoder().decode(BackendUser.self, from: userData)
            currentUser = user
            authToken = token
            isAuthenticated = true
            
            // Initialize services
            initializeServices()
            
            print("✅ User loaded from storage: \(user.email)")
        } catch {
            print("❌ Failed to load user from storage: \(error)")
            clearUserFromStorage()
        }
    }
    
    private func clearUserFromStorage() {
        UserDefaults.standard.removeObject(forKey: "current_user")
        UserDefaults.standard.removeObject(forKey: "auth_token")
        UserDefaults.standard.removeObject(forKey: "is_authenticated")
    }
    
    // MARK: - Notifications
    
    private func setupNotifications() {
        // Listen for app becoming active
        NotificationCenter.default.addObserver(
            forName: UIApplication.didBecomeActiveNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            self?.handleAppBecameActive()
        }
    }
    
    private func handleAppBecameActive() {
        guard let userId = currentUser?.id else { return }
        syncService.handleAppBecameActive(for: userId, categoryManager: CategoryManager.shared)
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
}

// MARK: - Convenience Methods

extension UserManager {
    
    /// Get current user ID
    var currentUserId: String? {
        return currentUser?.id
    }
    
    /// Check if WhatsApp is connected
    var isWhatsAppConnected: Bool {
        return currentUser?.isWhatsappVerified ?? false
    }
    
    /// Get user email
    var userEmail: String? {
        return currentUser?.email
    }
}
