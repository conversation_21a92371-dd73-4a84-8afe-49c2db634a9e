//
//  WhatsAppSettingsView.swift
//  Pebl
//
//  Created by AI Assistant on 7/3/25.
//

import SwiftUI

struct WhatsAppSettingsView: View {
    @StateObject private var whatsappService = WhatsAppIntegrationService.shared
    @StateObject private var syncService = SyncService.shared
    @EnvironmentObject var categoryManager: CategoryManager
    @EnvironmentObject var userManager: UserManager
    
    @State private var phoneNumber = ""
    @State private var verificationCode = ""
    @State private var showingPhoneInput = false
    @State private var showingVerificationInput = false
    @State private var showingDisconnectAlert = false
    
    var body: some View {
        NavigationView {
            List {
                // Status Section
                Section {
                    HStack {
                        Image(systemName: whatsappService.isVerified ? "checkmark.circle.fill" : "circle")
                            .foregroundColor(whatsappService.isVerified ? .green : .gray)
                        
                        VStack(alignment: .leading, spacing: 4) {
                            Text("WhatsApp Integration")
                                .font(.headline)
                            
                            Text(statusText)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        
                        Spacer()
                        
                        if whatsappService.isVerified {
                            Text("Connected")
                                .font(.caption)
                                .padding(.horizontal, 8)
                                .padding(.vertical, 4)
                                .background(Color.green.opacity(0.2))
                                .foregroundColor(.green)
                                .cornerRadius(8)
                        }
                    }
                    .padding(.vertical, 4)
                } header: {
                    Text("Status")
                }
                
                // Connection Section
                Section {
                    if !whatsappService.isLinked {
                        // Connect Button
                        Button(action: {
                            // Connect directly with phone number - no email needed
                            showingPhoneInput = true
                        }) {
                            HStack {
                                Image(systemName: "link")
                                Text("Connect WhatsApp")
                                Spacer()
                                Image(systemName: "chevron.right")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                        .disabled(whatsappService.isLinking)
                    } else if !whatsappService.isVerified {
                        // Verify Button
                        Button(action: {
                            showingVerificationInput = true
                        }) {
                            HStack {
                                Image(systemName: "key")
                                Text("Verify Number")
                                Spacer()
                                Image(systemName: "chevron.right")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                        .disabled(whatsappService.isVerifying)
                        
                        // Resend Code Button
                        Button(action: {
                            if let number = whatsappService.whatsappNumber {
                                whatsappService.linkWhatsAppNumber(number)
                            }
                        }) {
                            HStack {
                                Image(systemName: "arrow.clockwise")
                                Text("Resend Verification Code")
                            }
                        }
                        .disabled(whatsappService.isLinking)
                    } else {
                        // Connected - Show number and disconnect option
                        if let number = whatsappService.whatsappNumber {
                            HStack {
                                Image(systemName: "phone")
                                Text(number)
                                Spacer()
                            }
                        }
                        
                        Button(action: {
                            showingDisconnectAlert = true
                        }) {
                            HStack {
                                Image(systemName: "link.badge.minus")
                                Text("Disconnect WhatsApp")
                            }
                            .foregroundColor(.red)
                        }
                    }
                } header: {
                    Text("Connection")
                }
                
                // Sync Section
                if whatsappService.isVerified {
                    Section {
                        HStack {
                            Image(systemName: "arrow.triangle.2.circlepath")
                            Text("Last Sync")
                            Spacer()
                            Text(lastSyncText)
                                .foregroundColor(.secondary)
                        }
                        
                        Button(action: {
                            // Sync messages if WhatsApp is connected
                            if whatsappService.isVerified, let phoneNumber = whatsappService.connectedPhoneNumber {
                                // Use phone number as user identifier for sync
                                syncService.syncPendingMessages(for: phoneNumber, categoryManager: categoryManager)
                            }
                        }) {
                            HStack {
                                if syncService.isSyncing {
                                    ProgressView()
                                        .scaleEffect(0.8)
                                } else {
                                    Image(systemName: "arrow.clockwise")
                                }
                                Text("Sync Now")
                            }
                        }
                        .disabled(syncService.isSyncing)
                        
                        if let error = syncService.syncError {
                            HStack {
                                Image(systemName: "exclamationmark.triangle")
                                    .foregroundColor(.orange)
                                Text(error)
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                    } header: {
                        Text("Sync")
                    }
                }
                
                // Instructions Section
                Section {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("How it works:")
                            .font(.headline)

                        Text("1. Connect your WhatsApp number")
                        Text("2. Verify with the code sent to WhatsApp")
                        Text("3. Send messages to your WhatsApp Business number")
                        Text("4. Messages will automatically appear in Pebl")
                    }
                    .font(.caption)
                    .foregroundColor(.secondary)
                } header: {
                    Text("Instructions")
                }
            }
            .navigationTitle("WhatsApp")
            .navigationBarTitleDisplayMode(.large)
        }
        .sheet(isPresented: $showingPhoneInput) {
            PhoneNumberInputView(
                phoneNumber: $phoneNumber,
                isLoading: whatsappService.isLinking,
                error: whatsappService.linkingError
            ) {
                whatsappService.linkWhatsAppNumber(phoneNumber)
                showingPhoneInput = false
            }
        }
        .sheet(isPresented: $showingVerificationInput) {
            VerificationCodeInputView(
                verificationCode: $verificationCode,
                isLoading: whatsappService.isVerifying,
                error: whatsappService.verificationError
            ) {
                whatsappService.verifyWhatsAppNumber(with: verificationCode)
                showingVerificationInput = false
            }
        }
        .alert("Disconnect WhatsApp", isPresented: $showingDisconnectAlert) {
            Button("Cancel", role: .cancel) { }
            Button("Disconnect", role: .destructive) {
                whatsappService.disconnectWhatsApp()
            }
        } message: {
            Text("Are you sure you want to disconnect WhatsApp? You'll need to verify your number again to reconnect.")
        }
        .onAppear {
            // WhatsApp settings can be viewed and used without email authentication
            // Phone number verification through WhatsApp is sufficient
            whatsappService.initializeForPhoneAuth()
        }
    }

    // MARK: - Computed Properties

    private var statusText: String {
        if whatsappService.isVerified {
            return "Messages from WhatsApp will sync automatically"
        } else if whatsappService.isLinked {
            return "Verification required to complete setup"
        } else {
            return "Connect your WhatsApp to sync messages"
        }
    }

    private var lastSyncText: String {
        if let lastSync = syncService.lastSyncDate {
            let formatter = RelativeDateTimeFormatter()
            return formatter.localizedString(for: lastSync, relativeTo: Date())
        } else {
            return "Never"
        }
    }
}

// MARK: - Phone Number Input View

struct PhoneNumberInputView: View {
    @Binding var phoneNumber: String
    let isLoading: Bool
    let error: String?
    let onSubmit: () -> Void
    
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                VStack(alignment: .leading, spacing: 8) {
                    Text("Enter your WhatsApp number")
                        .font(.headline)
                    
                    Text("Include your country code (e.g., +1 for US)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    TextField("+1234567890", text: $phoneNumber)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .keyboardType(.phonePad)
                }
                
                if let error = error {
                    Text(error)
                        .foregroundColor(.red)
                        .font(.caption)
                }
                
                Button(action: onSubmit) {
                    HStack {
                        if isLoading {
                            ProgressView()
                                .scaleEffect(0.8)
                        }
                        Text("Send Verification Code")
                    }
                    .frame(maxWidth: .infinity)
                }
                .buttonStyle(.borderedProminent)
                .disabled(phoneNumber.isEmpty || isLoading)
                
                Spacer()
            }
            .padding()
            .navigationTitle("Connect WhatsApp")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
        }
    }
}

// MARK: - Verification Code Input View

struct VerificationCodeInputView: View {
    @Binding var verificationCode: String
    let isLoading: Bool
    let error: String?
    let onSubmit: () -> Void
    
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                VStack(alignment: .leading, spacing: 8) {
                    Text("Enter verification code")
                        .font(.headline)
                    
                    Text("Check your WhatsApp for the verification code")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    TextField("123456", text: $verificationCode)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .keyboardType(.numberPad)
                        .textContentType(.oneTimeCode)
                }
                
                if let error = error {
                    Text(error)
                        .foregroundColor(.red)
                        .font(.caption)
                }
                
                Button(action: onSubmit) {
                    HStack {
                        if isLoading {
                            ProgressView()
                                .scaleEffect(0.8)
                        }
                        Text("Verify")
                    }
                    .frame(maxWidth: .infinity)
                }
                .buttonStyle(.borderedProminent)
                .disabled(verificationCode.isEmpty || isLoading)
                
                Spacer()
            }
            .padding()
            .navigationTitle("Verify Number")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
        }
    }
}



#Preview {
    WhatsAppSettingsView()
        .environmentObject(CategoryManager())
}
