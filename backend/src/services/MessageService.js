const Message = require('../models/Message');
const User = require('../models/User');
const WhatsAppService = require('./WhatsAppService');
const CategorizationService = require('./CategorizationService');
const NotificationService = require('./NotificationService');
const logger = require('../utils/logger');

class MessageService {
  /**
   * Process an incoming WhatsApp message
   */
  async processWhatsAppMessage({ fromNumber, messageText, messageId, timestamp }) {
    try {
      // Find user by WhatsApp number
      const user = await WhatsAppService.findUserByWhatsAppNumber(fromNumber);
      
      if (!user) {
        logger.info('Message from unregistered WhatsApp number', { fromNumber });
        await this.handleUnregisteredUser(fromNumber, messageText);
        return;
      }

      // Check if message already exists (prevent duplicates)
      const existingMessage = await Message.findOne({ whatsappMessageId: messageId });
      if (existingMessage) {
        logger.info('Duplicate message ignored', { messageId });
        return;
      }

      // Parse and categorize the message
      const categorizationResult = await CategorizationService.categorizeMessage(
        messageText, 
        user._id
      );

      // Create message record
      const message = new Message({
        text: messageText,
        mainMessage: categorizationResult.parsedMessage.mainMessage,
        subcontent: categorizationResult.parsedMessage.subcontent,
        source: 'whatsapp',
        whatsappMessageId: messageId,
        whatsappTimestamp: timestamp,
        userId: user._id,
        categoryName: categorizationResult.categoryName,
        subcategoryName: categorizationResult.subcategoryName || '',
        aiCategorization: {
          confidence: categorizationResult.confidence,
          suggestedCategories: categorizationResult.suggestedCategories,
          processingTime: categorizationResult.processingTime,
          model: categorizationResult.model
        },
        timestamp: timestamp,
        syncStatus: 'pending'
      });

      await message.save();

      logger.info('WhatsApp message processed and saved', {
        userId: user._id,
        messageId,
        category: categorizationResult.categoryName,
        subcategory: categorizationResult.subcategoryName
      });

      // Send push notification to user's devices
      await NotificationService.notifyNewMessage(user, message);

      // Send confirmation back to WhatsApp (optional)
      if (user.preferences?.notificationSettings?.whatsappMessages) {
        await this.sendCategorizationConfirmation(fromNumber, categorizationResult);
      }

      return message;
    } catch (error) {
      logger.error('Error processing WhatsApp message:', error);
      throw error;
    }
  }

  /**
   * Handle messages from unregistered users
   */
  async handleUnregisteredUser(fromNumber, messageText) {
    try {
      const helpMessage = `Hi! It looks like this WhatsApp number isn't connected to a Pebl account yet.

To start using Pebl with WhatsApp:
1. Download the Pebl app from the App Store
2. Create an account or sign in
3. Go to Settings > WhatsApp Integration
4. Follow the setup instructions

Once connected, your messages will be automatically organized in the app!`;

      await WhatsAppService.sendMessage(fromNumber, helpMessage);
      
      logger.info('Help message sent to unregistered user', { fromNumber });
    } catch (error) {
      logger.error('Error handling unregistered user:', error);
    }
  }

  /**
   * Send categorization confirmation to user
   */
  async sendCategorizationConfirmation(whatsappNumber, categorizationResult) {
    try {
      let confirmationMessage = `✅ Message saved to "${categorizationResult.categoryName}"`;
      
      if (categorizationResult.subcategoryName) {
        confirmationMessage += ` > "${categorizationResult.subcategoryName}"`;
      }
      
      if (categorizationResult.confidence < 0.8) {
        confirmationMessage += `\n\n💡 Not sure about the category? You can move it in the app!`;
      }

      await WhatsAppService.sendMessage(whatsappNumber, confirmationMessage);
    } catch (error) {
      logger.error('Error sending categorization confirmation:', error);
      // Don't throw - this is not critical
    }
  }

  /**
   * Get messages for a user with filtering options
   */
  async getMessagesForUser(userId, options = {}) {
    try {
      const messages = await Message.findByUser(userId, options);
      return messages;
    } catch (error) {
      logger.error('Error getting messages for user:', error);
      throw error;
    }
  }

  /**
   * Get pending sync messages for a user
   */
  async getPendingSyncMessages(userId) {
    try {
      const messages = await Message.findPendingSync(userId);
      return messages;
    } catch (error) {
      logger.error('Error getting pending sync messages:', error);
      throw error;
    }
  }

  /**
   * Mark messages as synced
   */
  async markMessagesSynced(messageIds) {
    try {
      const result = await Message.updateMany(
        { _id: { $in: messageIds } },
        { 
          syncStatus: 'synced',
          lastSyncAttempt: new Date()
        }
      );

      logger.info('Messages marked as synced', { 
        count: result.modifiedCount,
        messageIds: messageIds.length 
      });

      return result;
    } catch (error) {
      logger.error('Error marking messages as synced:', error);
      throw error;
    }
  }

  /**
   * Retry failed sync messages
   */
  async retryFailedSyncMessages(userId) {
    try {
      const failedMessages = await Message.find({
        userId,
        syncStatus: 'failed',
        syncRetryCount: { $lt: 3 }
      });

      for (const message of failedMessages) {
        await message.resetSync();
      }

      logger.info('Failed sync messages reset for retry', { 
        userId,
        count: failedMessages.length 
      });

      return failedMessages;
    } catch (error) {
      logger.error('Error retrying failed sync messages:', error);
      throw error;
    }
  }

  /**
   * Delete expired messages
   */
  async deleteExpiredMessages(userId) {
    try {
      const user = await User.findById(userId);
      if (!user || !user.preferences?.autoDeleteExpiredMessages) {
        return { deletedCount: 0 };
      }

      const expirationDate = new Date();
      expirationDate.setDate(expirationDate.getDate() - user.preferences.messageExpirationDays);

      const result = await Message.deleteMany({
        userId,
        timestamp: { $lt: expirationDate },
        isCompleted: true // Only delete completed messages
      });

      logger.info('Expired messages deleted', { 
        userId,
        deletedCount: result.deletedCount 
      });

      return result;
    } catch (error) {
      logger.error('Error deleting expired messages:', error);
      throw error;
    }
  }

  /**
   * Get message statistics for a user
   */
  async getMessageStatistics(userId) {
    try {
      const stats = await Message.aggregate([
        { $match: { userId: mongoose.Types.ObjectId(userId) } },
        {
          $group: {
            _id: null,
            totalMessages: { $sum: 1 },
            whatsappMessages: {
              $sum: { $cond: [{ $eq: ['$source', 'whatsapp'] }, 1, 0] }
            },
            appMessages: {
              $sum: { $cond: [{ $eq: ['$source', 'app'] }, 1, 0] }
            },
            completedMessages: {
              $sum: { $cond: ['$isCompleted', 1, 0] }
            },
            pendingSyncMessages: {
              $sum: { $cond: [{ $eq: ['$syncStatus', 'pending'] }, 1, 0] }
            }
          }
        }
      ]);

      const categoryStats = await Message.aggregate([
        { $match: { userId: mongoose.Types.ObjectId(userId) } },
        {
          $group: {
            _id: '$categoryName',
            count: { $sum: 1 },
            lastMessage: { $max: '$timestamp' }
          }
        },
        { $sort: { count: -1 } }
      ]);

      return {
        overview: stats[0] || {
          totalMessages: 0,
          whatsappMessages: 0,
          appMessages: 0,
          completedMessages: 0,
          pendingSyncMessages: 0
        },
        categories: categoryStats
      };
    } catch (error) {
      logger.error('Error getting message statistics:', error);
      throw error;
    }
  }
}

module.exports = new MessageService();
