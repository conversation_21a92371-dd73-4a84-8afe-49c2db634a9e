// Unit tests for individual services without database dependencies

describe('Categorization Service Unit Tests', () => {
  let CategorizationService;

  beforeAll(() => {
    // Set test environment
    process.env.NODE_ENV = 'test';
    process.env.OPENAI_API_KEY = 'test-openai-key';
    
    // Import the service after setting environment
    CategorizationService = require('../src/services/CategorizationService');
  });

  describe('parseHashtagMessage', () => {
    it('should parse a simple hashtag correctly', () => {
      const result = CategorizationService.parseHashtagMessage('#shopping Buy groceries');
      
      expect(result.categoryPath).toBeTruthy();
      expect(result.categoryPath.category).toBe('Shopping');
      expect(result.categoryPath.subcategory).toBeNull();
      expect(result.cleanedMessage).toBe('Buy groceries');
      expect(result.originalMessage).toBe('#shopping Buy groceries');
    });

    it('should parse a subcategory hashtag correctly', () => {
      const result = CategorizationService.parseHashtagMessage('#work/meetings Schedule team sync');
      
      expect(result.categoryPath).toBeTruthy();
      expect(result.categoryPath.category).toBe('Work');
      expect(result.categoryPath.subcategory).toBe('Meetings');
      expect(result.cleanedMessage).toBe('Schedule team sync');
      expect(result.originalMessage).toBe('#work/meetings Schedule team sync');
    });

    it('should handle hashtag at the end of message', () => {
      const result = CategorizationService.parseHashtagMessage('Buy groceries #shopping');
      
      expect(result.categoryPath).toBeTruthy();
      expect(result.categoryPath.category).toBe('Shopping');
      expect(result.cleanedMessage).toBe('Buy groceries');
    });

    it('should handle message without hashtag', () => {
      const result = CategorizationService.parseHashtagMessage('Just a regular message');
      
      expect(result.categoryPath).toBeNull();
      expect(result.cleanedMessage).toBe('Just a regular message');
      expect(result.originalMessage).toBe('Just a regular message');
    });

    it('should normalize category names correctly', () => {
      const result = CategorizationService.parseHashtagMessage('#to_read/tech-articles Read this article');
      
      expect(result.categoryPath.category).toBe('To-Read');
      expect(result.categoryPath.subcategory).toBe('Tech-Articles');
    });
  });

  describe('normalizeCategory', () => {
    it('should normalize underscores and dashes', () => {
      const normalized = CategorizationService.normalizeCategory('to_read');
      expect(normalized).toBe('To-Read');
    });

    it('should handle mixed case', () => {
      const normalized = CategorizationService.normalizeCategory('SHOPPING');
      expect(normalized).toBe('Shopping');
    });

    it('should handle multiple words', () => {
      const normalized = CategorizationService.normalizeCategory('tech_articles');
      expect(normalized).toBe('Tech-Articles');
    });
  });

  describe('parseMessageContent', () => {
    it('should handle regular text messages', () => {
      const result = CategorizationService.parseMessageContent('Just a regular text message');

      expect(result.mainMessage).toBe('Just a regular text message');
      expect(result.subcontent).toBe('');
    });

    it('should detect URL messages', () => {
      const result = CategorizationService.parseMessageContent('https://example.com');

      expect(result.mainMessage).toBe('Link: example.com');
      expect(result.subcontent).toBe('https://example.com');
    });
  });

  describe('isURL', () => {
    it('should detect HTTP URLs', () => {
      expect(CategorizationService.isURL('http://example.com')).toBe(true);
    });

    it('should detect HTTPS URLs', () => {
      expect(CategorizationService.isURL('https://example.com')).toBe(true);
    });

    it('should reject non-URLs', () => {
      expect(CategorizationService.isURL('Just regular text')).toBe(false);
    });
  });
});

describe('WhatsApp Service Unit Tests', () => {
  let WhatsAppService;

  beforeAll(() => {
    // Set test environment
    process.env.WHATSAPP_ACCESS_TOKEN = 'test-token';
    process.env.WHATSAPP_PHONE_NUMBER_ID = 'test-phone-id';

    // Import the service after setting environment
    WhatsAppService = require('../src/services/WhatsAppService');
  });

  describe('linkUserAccount', () => {
    it('should be a function', () => {
      expect(typeof WhatsAppService.linkUserAccount).toBe('function');
    });
  });

  describe('verifyUserNumber', () => {
    it('should be a function', () => {
      expect(typeof WhatsAppService.verifyUserNumber).toBe('function');
    });
  });

  describe('findUserByWhatsAppNumber', () => {
    it('should be a function', () => {
      expect(typeof WhatsAppService.findUserByWhatsAppNumber).toBe('function');
    });
  });

  describe('sendWelcomeMessage', () => {
    it('should be a function', () => {
      expect(typeof WhatsAppService.sendWelcomeMessage).toBe('function');
    });
  });
});

describe('Message Service Unit Tests', () => {
  let MessageService;

  beforeAll(() => {
    // Import the service
    MessageService = require('../src/services/MessageService');
  });

  describe('handleUnregisteredUser', () => {
    it('should be a function', () => {
      expect(typeof MessageService.handleUnregisteredUser).toBe('function');
    });
  });

  describe('markMessagesSynced', () => {
    it('should be a function', () => {
      expect(typeof MessageService.markMessagesSynced).toBe('function');
    });
  });

  describe('getPendingSyncMessages', () => {
    it('should be a function', () => {
      expect(typeof MessageService.getPendingSyncMessages).toBe('function');
    });
  });
});

describe('Notification Service Unit Tests', () => {
  let NotificationService;

  beforeAll(() => {
    // Set test environment
    process.env.NODE_ENV = 'test';

    // Import the service after setting environment
    NotificationService = require('../src/services/NotificationService');
  });

  describe('createNotificationBody', () => {
    it('should create notification body for text messages', () => {
      const message = {
        mainMessage: 'Buy groceries',
        categoryName: 'Shopping'
      };

      const body = NotificationService.createNotificationBody(message);
      expect(typeof body).toBe('string');
      expect(body.length).toBeGreaterThan(0);
    });
  });

  describe('notifyNewMessage', () => {
    it('should be a function', () => {
      expect(typeof NotificationService.notifyNewMessage).toBe('function');
    });
  });

  describe('notifySyncRequired', () => {
    it('should be a function', () => {
      expect(typeof NotificationService.notifySyncRequired).toBe('function');
    });
  });
});
