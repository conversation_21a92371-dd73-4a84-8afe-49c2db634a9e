const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const userSchema = new mongoose.Schema({
  // Basic user information
  email: {
    type: String,
    required: true,
    lowercase: true,
    trim: true
  },

  // WhatsApp integration
  whatsappNumber: {
    type: String,
    sparse: true, // Allows multiple null values
    trim: true
  },
  
  isWhatsappVerified: {
    type: Boolean,
    default: false
  },
  
  // Device information for push notifications
  deviceTokens: [{
    token: String,
    platform: {
      type: String,
      enum: ['ios', 'android'],
      default: 'ios'
    },
    lastUsed: {
      type: Date,
      default: Date.now
    }
  }],
  
  // User preferences
  preferences: {
    messageExpirationDays: {
      type: Number,
      default: 60
    },
    autoDeleteExpiredMessages: {
      type: Boolean,
      default: true
    },
    enableWhatsappSync: {
      type: Boolean,
      default: true
    },
    notificationSettings: {
      pushNotifications: {
        type: Boolean,
        default: true
      },
      whatsappMessages: {
        type: Boolean,
        default: true
      }
    }
  },
  
  // Categories sync
  lastCategorySync: {
    type: Date,
    default: Date.now
  },
  
  // Account status
  isActive: {
    type: Boolean,
    default: true
  },
  
  // Timestamps
  createdAt: {
    type: Date,
    default: Date.now
  },
  
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Update the updatedAt field before saving
userSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Index for efficient queries
userSchema.index({ whatsappNumber: 1 }, { unique: true, sparse: true });
userSchema.index({ email: 1 }, { unique: true });
userSchema.index({ 'deviceTokens.token': 1 });

// Instance methods
userSchema.methods.addDeviceToken = function(token, platform = 'ios') {
  // Remove existing token if it exists
  this.deviceTokens = this.deviceTokens.filter(dt => dt.token !== token);
  
  // Add new token
  this.deviceTokens.push({
    token,
    platform,
    lastUsed: new Date()
  });
  
  // Keep only the 5 most recent tokens
  if (this.deviceTokens.length > 5) {
    this.deviceTokens = this.deviceTokens
      .sort((a, b) => b.lastUsed - a.lastUsed)
      .slice(0, 5);
  }
  
  return this.save();
};

userSchema.methods.removeDeviceToken = function(token) {
  this.deviceTokens = this.deviceTokens.filter(dt => dt.token !== token);
  return this.save();
};

userSchema.methods.getActiveDeviceTokens = function() {
  // Return tokens used in the last 30 days
  const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
  return this.deviceTokens
    .filter(dt => dt.lastUsed > thirtyDaysAgo)
    .map(dt => dt.token);
};

module.exports = mongoose.model('User', userSchema);
