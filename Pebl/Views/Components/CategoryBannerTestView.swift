//
//  CategoryBannerTestView.swift
//  Pebl
//
//  Created by AI Assistant on 7/28/25.
//

import SwiftUI

/// Test view to verify the category banner functionality works
struct CategoryBannerTestView: View {
    @EnvironmentObject var categoryManager: CategoryManager
    @State private var inputMessage: String = ""
    
    var body: some View {
        CategoryBannerWrapper {
            VStack(spacing: 20) {
                Text("Category Banner Test")
                    .font(.title)
                    .padding()
                
                VStack(spacing: 15) {
                    TextField("Enter a message to categorize...", text: $inputMessage)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .padding(.horizontal)
                    
                    <PERSON><PERSON>("Add Message") {
                        addMessage()
                    }
                    .buttonStyle(.borderedProminent)
                    .disabled(inputMessage.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
                    
                    Divider()
                    
                    Text("Test Banner Types:")
                        .font(.headline)
                    
                    VStack(spacing: 10) {
                        <PERSON><PERSON>("Show New Category Banner") {
                            showCategoryBanner(categoryName: "New Category", isNewCategory: true)
                        }
                        .buttonStyle(.bordered)
                        
                        But<PERSON>("Show Existing Category Banner") {
                            showCategoryBanner(categoryName: "Existing Category", isNewCategory: false)
                        }
                        .buttonStyle(.bordered)
                        
                        Button("Show Subcategory Banner") {
                            showCategoryBanner(
                                categoryName: "Movies to Watch",
                                subcategoryName: "Comedy",
                                isNewCategory: false
                            )
                        }
                        .buttonStyle(.bordered)
                    }
                }
                
                Spacer()
            }
            .padding()
        }
    }
    
    private func addMessage() {
        let message = inputMessage.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !message.isEmpty else { return }
        
        inputMessage = "" // Clear input immediately
        
        // Add message using the category manager (this will automatically show the banner)
        categoryManager.addMessageWithDetails(message) { result in
            if result.success {
                categoryManager.saveToFile()
                print("✅ Message added successfully to \(result.categoryName ?? "unknown category")")
            } else {
                print("⚠️ Failed to add message: \(message)")
            }
        }
    }
}

#Preview {
    CategoryBannerTestView()
        .environmentObject(CategoryManager.shared)
}
