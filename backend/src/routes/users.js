const express = require('express');
const User = require('../models/User');
const logger = require('../utils/logger');

const router = express.Router();

// Get user profile
router.get('/:userId', async (req, res) => {
  try {
    const { userId } = req.params;
    
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        error: 'User not found'
      });
    }

    res.status(200).json({
      success: true,
      user: {
        id: user._id,
        email: user.email,
        whatsappNumber: user.whatsappNumber,
        isWhatsappVerified: user.isWhatsappVerified,
        preferences: user.preferences,
        lastCategorySync: user.lastCategorySync,
        createdAt: user.createdAt
      }
    });

  } catch (error) {
    logger.error('Error getting user profile:', error);
    res.status(500).json({
      error: 'Failed to get user profile',
      details: error.message
    });
  }
});

// Update user preferences
router.patch('/:userId/preferences', async (req, res) => {
  try {
    const { userId } = req.params;
    const { preferences } = req.body;
    
    if (!preferences) {
      return res.status(400).json({
        error: 'Preferences object is required'
      });
    }

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        error: 'User not found'
      });
    }

    // Update preferences
    user.preferences = { ...user.preferences, ...preferences };
    await user.save();

    logger.info('User preferences updated', { userId, preferences });

    res.status(200).json({
      success: true,
      preferences: user.preferences
    });

  } catch (error) {
    logger.error('Error updating user preferences:', error);
    res.status(500).json({
      error: 'Failed to update preferences',
      details: error.message
    });
  }
});

// Update last category sync timestamp
router.patch('/:userId/sync-timestamp', async (req, res) => {
  try {
    const { userId } = req.params;
    
    const user = await User.findByIdAndUpdate(
      userId,
      { lastCategorySync: new Date() },
      { new: true }
    );

    if (!user) {
      return res.status(404).json({
        error: 'User not found'
      });
    }

    res.status(200).json({
      success: true,
      lastCategorySync: user.lastCategorySync
    });

  } catch (error) {
    logger.error('Error updating sync timestamp:', error);
    res.status(500).json({
      error: 'Failed to update sync timestamp',
      details: error.message
    });
  }
});

// Deactivate user account
router.patch('/:userId/deactivate', async (req, res) => {
  try {
    const { userId } = req.params;
    
    const user = await User.findByIdAndUpdate(
      userId,
      { 
        isActive: false,
        whatsappNumber: null,
        isWhatsappVerified: false,
        deviceTokens: []
      },
      { new: true }
    );

    if (!user) {
      return res.status(404).json({
        error: 'User not found'
      });
    }

    logger.info('User account deactivated', { userId });

    res.status(200).json({
      success: true,
      message: 'Account deactivated successfully'
    });

  } catch (error) {
    logger.error('Error deactivating user account:', error);
    res.status(500).json({
      error: 'Failed to deactivate account',
      details: error.message
    });
  }
});

// Reactivate user account
router.patch('/:userId/reactivate', async (req, res) => {
  try {
    const { userId } = req.params;
    
    const user = await User.findByIdAndUpdate(
      userId,
      { isActive: true },
      { new: true }
    );

    if (!user) {
      return res.status(404).json({
        error: 'User not found'
      });
    }

    logger.info('User account reactivated', { userId });

    res.status(200).json({
      success: true,
      message: 'Account reactivated successfully'
    });

  } catch (error) {
    logger.error('Error reactivating user account:', error);
    res.status(500).json({
      error: 'Failed to reactivate account',
      details: error.message
    });
  }
});

module.exports = router;
