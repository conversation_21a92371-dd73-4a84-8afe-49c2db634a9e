{"name": "pebl-backend", "version": "1.0.0", "description": "Backend service for Pebl app with WhatsApp Business API integration", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"axios": "^1.6.0", "bcryptjs": "^2.4.3", "body-parser": "^1.20.2", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.5.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.0", "node-cron": "^3.0.3", "winston": "^3.11.0"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.1", "supertest": "^6.3.3"}, "keywords": ["whatsapp", "business-api", "categorization", "ai", "messaging"], "author": "Pebl Team", "license": "MIT"}