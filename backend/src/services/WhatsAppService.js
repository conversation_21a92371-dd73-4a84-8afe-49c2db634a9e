const axios = require('axios');
const User = require('../models/User');
const logger = require('../utils/logger');

class WhatsAppService {
  constructor() {
    this.accessToken = process.env.WHATSAPP_ACCESS_TOKEN;
    this.phoneNumberId = process.env.WHATSAPP_PHONE_NUMBER_ID;
    this.baseURL = 'https://graph.facebook.com/v18.0';
    
    // Store verification codes temporarily (in production, use Redis)
    this.verificationCodes = new Map();
  }

  /**
   * Send a text message via WhatsApp Business API
   */
  async sendMessage(to, message) {
    try {
      const url = `${this.baseURL}/${this.phoneNumberId}/messages`;
      
      const payload = {
        messaging_product: 'whatsapp',
        to: to,
        type: 'text',
        text: {
          body: message
        }
      };

      const response = await axios.post(url, payload, {
        headers: {
          'Authorization': `Bearer ${this.accessToken}`,
          'Content-Type': 'application/json'
        }
      });

      logger.info('WhatsApp message sent successfully', {
        to,
        messageId: response.data.messages[0].id
      });

      return {
        success: true,
        messageId: response.data.messages[0].id
      };
    } catch (error) {
      logger.error('Failed to send WhatsApp message:', error.response?.data || error.message);
      throw new Error(`Failed to send WhatsApp message: ${error.response?.data?.error?.message || error.message}`);
    }
  }

  /**
   * Send a template message (for verification codes, etc.)
   */
  async sendTemplateMessage(to, templateName, parameters = []) {
    try {
      const url = `${this.baseURL}/${this.phoneNumberId}/messages`;
      
      const payload = {
        messaging_product: 'whatsapp',
        to: to,
        type: 'template',
        template: {
          name: templateName,
          language: {
            code: 'en'
          },
          components: parameters.length > 0 ? [{
            type: 'body',
            parameters: parameters.map(param => ({
              type: 'text',
              text: param
            }))
          }] : []
        }
      };

      const response = await axios.post(url, payload, {
        headers: {
          'Authorization': `Bearer ${this.accessToken}`,
          'Content-Type': 'application/json'
        }
      });

      return {
        success: true,
        messageId: response.data.messages[0].id
      };
    } catch (error) {
      logger.error('Failed to send WhatsApp template message:', error.response?.data || error.message);
      throw new Error(`Failed to send template message: ${error.response?.data?.error?.message || error.message}`);
    }
  }

  /**
   * Link a WhatsApp number to a user account
   */
  async linkUserAccount(userId, whatsappNumber) {
    try {
      // Check if the number is already linked to another user
      const existingUser = await User.findOne({ 
        whatsappNumber: whatsappNumber,
        _id: { $ne: userId }
      });

      if (existingUser) {
        throw new Error('This WhatsApp number is already linked to another account');
      }

      // Generate verification code
      const verificationCode = Math.floor(100000 + Math.random() * 900000).toString();
      
      // Store verification code temporarily (expires in 10 minutes)
      this.verificationCodes.set(userId, {
        code: verificationCode,
        whatsappNumber: whatsappNumber,
        expiresAt: Date.now() + 10 * 60 * 1000
      });

      // Send verification code via WhatsApp
      const message = `Your Pebl verification code is: ${verificationCode}. This code will expire in 10 minutes.`;
      await this.sendMessage(whatsappNumber, message);

      logger.info('Verification code sent', { userId, whatsappNumber });

      return {
        success: true,
        verificationCode: verificationCode // Remove this in production
      };
    } catch (error) {
      logger.error('Error linking user account:', error);
      throw error;
    }
  }

  /**
   * Verify a user's WhatsApp number
   */
  async verifyUserNumber(userId, verificationCode) {
    try {
      const storedData = this.verificationCodes.get(userId);
      
      if (!storedData) {
        throw new Error('No verification code found for this user');
      }

      if (Date.now() > storedData.expiresAt) {
        this.verificationCodes.delete(userId);
        throw new Error('Verification code has expired');
      }

      if (storedData.code !== verificationCode) {
        throw new Error('Invalid verification code');
      }

      // Update user with verified WhatsApp number
      await User.findByIdAndUpdate(userId, {
        whatsappNumber: storedData.whatsappNumber,
        isWhatsappVerified: true
      });

      // Clean up verification code
      this.verificationCodes.delete(userId);

      logger.info('WhatsApp number verified successfully', { userId, whatsappNumber: storedData.whatsappNumber });

      return {
        verified: true,
        whatsappNumber: storedData.whatsappNumber
      };
    } catch (error) {
      logger.error('Error verifying user number:', error);
      throw error;
    }
  }

  /**
   * Get user's WhatsApp integration status
   */
  async getUserIntegrationStatus(userId) {
    try {
      const user = await User.findById(userId);
      
      if (!user) {
        throw new Error('User not found');
      }

      return {
        hasWhatsappNumber: !!user.whatsappNumber,
        isVerified: user.isWhatsappVerified,
        whatsappNumber: user.whatsappNumber,
        enableSync: user.preferences?.enableWhatsappSync || false
      };
    } catch (error) {
      logger.error('Error getting user integration status:', error);
      throw error;
    }
  }

  /**
   * Find user by WhatsApp number
   */
  async findUserByWhatsAppNumber(whatsappNumber) {
    try {
      const user = await User.findOne({
        whatsappNumber: whatsappNumber,
        isWhatsappVerified: true,
        isActive: true
      });

      return user;
    } catch (error) {
      logger.error('Error finding user by WhatsApp number:', error);
      throw error;
    }
  }

  /**
   * Send welcome message to new users
   */
  async sendWelcomeMessage(whatsappNumber) {
    const welcomeMessage = `Welcome to Pebl! 🎉

Your WhatsApp is now connected to your Pebl app. You can now send messages here and they'll automatically appear in your Pebl app, organized by category.

Try sending a message like:
• "Buy groceries for dinner"
• "Read the new book about AI"
• "#work Schedule meeting with team"

Your messages will be intelligently categorized and synced to your app!`;

    try {
      await this.sendMessage(whatsappNumber, welcomeMessage);
      logger.info('Welcome message sent', { whatsappNumber });
    } catch (error) {
      logger.error('Failed to send welcome message:', error);
    }
  }
}

module.exports = new WhatsAppService();
