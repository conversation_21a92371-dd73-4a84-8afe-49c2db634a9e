//
//  CategoryBannerTests.swift
//  Pebl
//
//  Created by AI Assistant on 7/28/25.
//

import XCTest
import Swift<PERSON>
@testable import Pebl

class CategoryBannerTests: XCTestCase {
    
    func testCategorizationAddResultInitialization() {
        // Test successful result
        let successResult = CategorizationAddResult(
            success: true,
            categoryName: "Shopping",
            subcategoryName: nil,
            isNewCategory: false
        )
        
        XCTAssertTrue(successResult.success)
        XCTAssertEqual(successResult.categoryName, "Shopping")
        XCTAssertNil(successResult.subcategoryName)
        XCTAssertFalse(successResult.isNewCategory)
        
        // Test result with subcategory
        let subcategoryResult = CategorizationAddResult(
            success: true,
            categoryName: "Movies to Watch",
            subcategoryName: "Comedy",
            isNewCategory: true
        )
        
        XCTAssertTrue(subcategoryResult.success)
        XCTAssertEqual(subcategoryResult.categoryName, "Movies to Watch")
        XCTAssertEqual(subcategoryResult.subcategoryName, "Comedy")
        XCTAssertTrue(subcategoryResult.isNewCategory)
        
        // Test failure result
        let failureResult = CategorizationAddResult(success: false)
        
        XCTAssertFalse(failureResult.success)
        XCTAssertNil(failureResult.categoryName)
        XCTAssertNil(failureResult.subcategoryName)
        XCTAssertFalse(failureResult.isNewCategory)
    }
    
    func testCategoryBannerInfoInitialization() {
        // Test basic category
        let basicInfo = CategoryBannerInfo(
            categoryName: "To-Do",
            subcategoryName: nil,
            isNewCategory: false
        )
        
        XCTAssertEqual(basicInfo.categoryName, "To-Do")
        XCTAssertNil(basicInfo.subcategoryName)
        XCTAssertFalse(basicInfo.isNewCategory)
        
        // Test with subcategory
        let subcategoryInfo = CategoryBannerInfo(
            categoryName: "Shopping",
            subcategoryName: "Electronics",
            isNewCategory: true
        )
        
        XCTAssertEqual(subcategoryInfo.categoryName, "Shopping")
        XCTAssertEqual(subcategoryInfo.subcategoryName, "Electronics")
        XCTAssertTrue(subcategoryInfo.isNewCategory)
    }
    
    func testCategoryManagerDetailedAddMessage() {
        let categoryManager = CategoryManager()
        let expectation = XCTestExpectation(description: "Message added with details")
        
        // Add a simple message
        categoryManager.addMessageWithDetails("Buy groceries") { result in
            XCTAssertTrue(result.success)
            XCTAssertNotNil(result.categoryName)
            expectation.fulfill()
        }
        
        wait(for: [expectation], timeout: 10.0)
    }
}
