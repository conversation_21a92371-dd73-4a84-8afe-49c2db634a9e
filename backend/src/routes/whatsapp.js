const express = require('express');
const crypto = require('crypto');
const WhatsAppService = require('../services/WhatsAppService');
const MessageService = require('../services/MessageService');
const logger = require('../utils/logger');

const router = express.Router();

// Webhook verification (GET request from WhatsApp)
router.get('/webhook', (req, res) => {
  const mode = req.query['hub.mode'];
  const token = req.query['hub.verify_token'];
  const challenge = req.query['hub.challenge'];

  // Verify the webhook
  if (mode === 'subscribe' && token === process.env.WHATSAPP_WEBHOOK_VERIFY_TOKEN) {
    logger.info('WhatsApp webhook verified successfully');
    res.status(200).send(challenge);
  } else {
    logger.warn('WhatsApp webhook verification failed', { mode, token });
    res.status(403).send('Forbidden');
  }
});

// Webhook for receiving messages (POST request from Whats<PERSON>pp)
router.post('/webhook', async (req, res) => {
  try {
    // Verify the webhook signature
    const signature = req.headers['x-hub-signature-256'];
    if (!verifyWebhookSignature(req.body, signature)) {
      logger.warn('Invalid webhook signature');
      return res.status(401).send('Unauthorized');
    }

    const body = req.body;
    
    // Check if this is a message event
    if (body.object === 'whatsapp_business_account') {
      for (const entry of body.entry) {
        for (const change of entry.changes) {
          if (change.field === 'messages') {
            await handleIncomingMessage(change.value);
          }
        }
      }
    }

    res.status(200).send('OK');
  } catch (error) {
    logger.error('Error processing WhatsApp webhook:', error);
    res.status(500).send('Internal Server Error');
  }
});

// Send a message via WhatsApp (for testing or automated responses)
router.post('/send-message', async (req, res) => {
  try {
    const { to, message } = req.body;
    
    if (!to || !message) {
      return res.status(400).json({
        error: 'Missing required fields: to, message'
      });
    }

    const result = await WhatsAppService.sendMessage(to, message);
    
    logger.info('Message sent via WhatsApp', { to, messageId: result.messageId });
    
    res.status(200).json({
      success: true,
      messageId: result.messageId
    });
  } catch (error) {
    logger.error('Error sending WhatsApp message:', error);
    res.status(500).json({
      error: 'Failed to send message',
      details: error.message
    });
  }
});

// Get WhatsApp integration status for a user
router.get('/status/:userId', async (req, res) => {
  try {
    const { userId } = req.params;
    const status = await WhatsAppService.getUserIntegrationStatus(userId);
    
    res.status(200).json(status);
  } catch (error) {
    logger.error('Error getting WhatsApp status:', error);
    res.status(500).json({
      error: 'Failed to get status',
      details: error.message
    });
  }
});

// Link WhatsApp number to user account
router.post('/link-account', async (req, res) => {
  try {
    const { userId, whatsappNumber } = req.body;
    
    if (!userId || !whatsappNumber) {
      return res.status(400).json({
        error: 'Missing required fields: userId, whatsappNumber'
      });
    }

    const result = await WhatsAppService.linkUserAccount(userId, whatsappNumber);
    
    res.status(200).json({
      success: true,
      verificationCode: result.verificationCode
    });
  } catch (error) {
    logger.error('Error linking WhatsApp account:', error);
    res.status(500).json({
      error: 'Failed to link account',
      details: error.message
    });
  }
});

// Verify WhatsApp number
router.post('/verify-number', async (req, res) => {
  try {
    const { userId, verificationCode } = req.body;
    
    if (!userId || !verificationCode) {
      return res.status(400).json({
        error: 'Missing required fields: userId, verificationCode'
      });
    }

    const result = await WhatsAppService.verifyUserNumber(userId, verificationCode);
    
    res.status(200).json({
      success: result.verified,
      message: result.verified ? 'Number verified successfully' : 'Invalid verification code'
    });
  } catch (error) {
    logger.error('Error verifying WhatsApp number:', error);
    res.status(500).json({
      error: 'Failed to verify number',
      details: error.message
    });
  }
});

// Helper function to verify webhook signature
function verifyWebhookSignature(payload, signature) {
  if (!signature) return false;
  
  const expectedSignature = crypto
    .createHmac('sha256', process.env.WHATSAPP_WEBHOOK_VERIFY_TOKEN)
    .update(JSON.stringify(payload))
    .digest('hex');
  
  const receivedSignature = signature.replace('sha256=', '');
  
  return crypto.timingSafeEqual(
    Buffer.from(expectedSignature, 'hex'),
    Buffer.from(receivedSignature, 'hex')
  );
}

// Handle incoming WhatsApp messages
async function handleIncomingMessage(messageData) {
  try {
    if (!messageData.messages) return;

    for (const message of messageData.messages) {
      // Only process text messages for now
      if (message.type !== 'text') {
        logger.info('Skipping non-text message', { type: message.type });
        continue;
      }

      const fromNumber = message.from;
      const messageText = message.text.body;
      const messageId = message.id;
      const timestamp = new Date(parseInt(message.timestamp) * 1000);

      logger.info('Processing WhatsApp message', {
        from: fromNumber,
        messageId,
        text: messageText.substring(0, 50) + '...'
      });

      // Process the message
      await MessageService.processWhatsAppMessage({
        fromNumber,
        messageText,
        messageId,
        timestamp
      });
    }
  } catch (error) {
    logger.error('Error handling incoming message:', error);
  }
}

module.exports = router;
