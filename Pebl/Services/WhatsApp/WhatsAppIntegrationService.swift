//
//  WhatsAppIntegrationService.swift
//  Pebl
//
//  Created by AI Assistant on 7/3/25.
//

import Foundation
import Combine

/// Service for managing WhatsApp integration
class WhatsAppIntegrationService: ObservableObject {
    
    // MARK: - Singleton
    
    static let shared = WhatsAppIntegrationService()
    
    private init() {}
    
    // MARK: - Published Properties
    
    @Published var isLinked = false
    @Published var isVerified = false
    @Published var whatsappNumber: String?
    @Published var isLinking = false
    @Published var isVerifying = false
    @Published var linkingError: String?
    @Published var verificationError: String?
    
    // MARK: - Private Properties
    
    private let networkService = NetworkService.shared
    private var cancellables = Set<AnyCancellable>()
    private var currentUserId: String?
    
    // MARK: - Public Methods
    
    /// Initialize WhatsApp integration status for a user
    func initialize(for userId: String) {
        self.currentUserId = userId
        loadIntegrationStatus()
    }

    /// Initialize WhatsApp integration for phone-based authentication
    func initializeForPhoneAuth() {
        // Load any existing WhatsApp connection from local storage
        loadIntegrationStatus()
    }

    /// Get the connected phone number if available
    var connectedPhoneNumber: String? {
        return whatsappNumber
    }
    
    /// Link WhatsApp number to user account
    func linkWhatsAppNumber(_ phoneNumber: String) {
        // No userId required - we'll create/find user by phone number
        
        guard !phoneNumber.isEmpty else {
            linkingError = "Phone number cannot be empty"
            return
        }
        
        // Validate phone number format
        let cleanedNumber = cleanPhoneNumber(phoneNumber)
        guard isValidPhoneNumber(cleanedNumber) else {
            linkingError = "Please enter a valid phone number with country code (e.g., +**********)"
            return
        }
        
        isLinking = true
        linkingError = nil
        
        print("🔗 Linking WhatsApp number: \(cleanedNumber)")

        // First register/authenticate user with phone number, then link WhatsApp
        registerUserWithPhoneNumber(cleanedNumber) { [weak self] success, userId in
            if success, let userId = userId {
                self?.currentUserId = userId
                self?.linkWhatsAppToUser(userId: userId, phoneNumber: cleanedNumber)
            } else {
                self?.isLinking = false
                self?.linkingError = "Failed to register user with phone number"
            }
        }
    }

    private func registerUserWithPhoneNumber(_ phoneNumber: String, completion: @escaping (Bool, String?) -> Void) {
        networkService.registerUser(phoneNumber: phoneNumber)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { completionResult in
                    if case .failure = completionResult {
                        completion(false, nil)
                    }
                },
                receiveValue: { response in
                    completion(response.success, response.user.id)
                }
            )
            .store(in: &cancellables)
    }

    private func linkWhatsAppToUser(userId: String, phoneNumber: String) {
        networkService.linkWhatsAppAccount(userId: userId, whatsappNumber: phoneNumber)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    self?.isLinking = false
                    
                    switch completion {
                    case .finished:
                        print("✅ WhatsApp linking initiated successfully")
                    case .failure(let error):
                        self?.linkingError = error.localizedDescription
                        print("❌ WhatsApp linking failed: \(error.localizedDescription)")
                    }
                },
                receiveValue: { [weak self] response in
                    if response.success {
                        self?.whatsappNumber = phoneNumber
                        self?.isLinked = true
                        print("📱 Verification code sent to \(phoneNumber)")
                    } else {
                        self?.linkingError = "Failed to send verification code"
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    /// Verify WhatsApp number with verification code
    func verifyWhatsAppNumber(with code: String) {
        guard let userId = currentUserId else {
            verificationError = "Please connect your WhatsApp number first"
            return
        }
        
        guard !code.isEmpty else {
            verificationError = "Verification code cannot be empty"
            return
        }
        
        isVerifying = true
        verificationError = nil
        
        print("🔐 Verifying WhatsApp number with code: \(code)")
        
        networkService.verifyWhatsAppNumber(userId: userId, verificationCode: code)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    self?.isVerifying = false
                    
                    switch completion {
                    case .finished:
                        print("✅ WhatsApp verification completed")
                    case .failure(let error):
                        self?.verificationError = error.localizedDescription
                        print("❌ WhatsApp verification failed: \(error.localizedDescription)")
                    }
                },
                receiveValue: { [weak self] response in
                    if response.success {
                        self?.isVerified = true
                        self?.saveIntegrationStatus()
                        print("🎉 WhatsApp number verified successfully!")
                    } else {
                        self?.verificationError = response.message
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    /// Disconnect WhatsApp integration
    func disconnectWhatsApp() {
        isLinked = false
        isVerified = false
        whatsappNumber = nil
        clearIntegrationStatus()
        
        print("🔌 WhatsApp integration disconnected")
    }
    
    /// Refresh integration status from backend
    func refreshStatus() {
        loadIntegrationStatus()
    }
    
    // MARK: - Private Methods
    
    private func loadIntegrationStatus() {
        guard let userId = currentUserId else { return }
        
        networkService.getWhatsAppStatus(userId: userId)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { completion in
                    switch completion {
                    case .finished:
                        break
                    case .failure(let error):
                        print("❌ Failed to load WhatsApp status: \(error.localizedDescription)")
                    }
                },
                receiveValue: { [weak self] response in
                    self?.isLinked = response.hasWhatsappNumber
                    self?.isVerified = response.isVerified
                    self?.whatsappNumber = response.whatsappNumber
                    
                    print("📱 WhatsApp status loaded - Linked: \(response.hasWhatsappNumber), Verified: \(response.isVerified)")
                }
            )
            .store(in: &cancellables)
    }
    
    private func saveIntegrationStatus() {
        guard let userId = currentUserId else { return }
        
        UserDefaults.standard.set(isLinked, forKey: "whatsapp_linked_\(userId)")
        UserDefaults.standard.set(isVerified, forKey: "whatsapp_verified_\(userId)")
        UserDefaults.standard.set(whatsappNumber, forKey: "whatsapp_number_\(userId)")
    }
    
    private func clearIntegrationStatus() {
        guard let userId = currentUserId else { return }
        
        UserDefaults.standard.removeObject(forKey: "whatsapp_linked_\(userId)")
        UserDefaults.standard.removeObject(forKey: "whatsapp_verified_\(userId)")
        UserDefaults.standard.removeObject(forKey: "whatsapp_number_\(userId)")
    }
    
    private func cleanPhoneNumber(_ phoneNumber: String) -> String {
        // Remove all non-digit characters except +
        let cleaned = phoneNumber.replacingOccurrences(of: "[^+0-9]", with: "", options: .regularExpression)
        
        // Ensure it starts with +
        if cleaned.hasPrefix("+") {
            return cleaned
        } else {
            return "+" + cleaned
        }
    }
    
    private func isValidPhoneNumber(_ phoneNumber: String) -> Bool {
        // Basic validation: starts with + and has 10-15 digits
        let pattern = "^\\+[1-9]\\d{9,14}$"
        let regex = try? NSRegularExpression(pattern: pattern)
        let range = NSRange(location: 0, length: phoneNumber.utf16.count)
        return regex?.firstMatch(in: phoneNumber, options: [], range: range) != nil
    }
}

// MARK: - Integration Status

struct WhatsAppIntegrationStatus {
    let isLinked: Bool
    let isVerified: Bool
    let whatsappNumber: String?
    let enableSync: Bool
    
    var isFullySetup: Bool {
        return isLinked && isVerified
    }
    
    var statusDescription: String {
        if isFullySetup {
            return "Connected and verified"
        } else if isLinked {
            return "Linked but not verified"
        } else {
            return "Not connected"
        }
    }
}
