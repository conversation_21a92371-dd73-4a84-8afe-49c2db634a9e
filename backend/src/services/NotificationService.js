const logger = require('../utils/logger');

class NotificationService {
  constructor() {
    // In production, initialize APNS (Apple Push Notification Service)
    // For now, we'll simulate push notifications
    this.isProduction = process.env.NODE_ENV === 'production';
  }

  /**
   * Send push notification for new message
   */
  async notifyNewMessage(user, message) {
    try {
      if (!user.preferences?.notificationSettings?.pushNotifications) {
        logger.info('Push notifications disabled for user', { userId: user._id });
        return;
      }

      const deviceTokens = user.getActiveDeviceTokens();
      
      if (deviceTokens.length === 0) {
        logger.info('No active device tokens for user', { userId: user._id });
        return;
      }

      const notification = {
        title: 'New Message Categorized',
        body: this.createNotificationBody(message),
        data: {
          messageId: message._id.toString(),
          categoryName: message.categoryName,
          subcategoryName: message.subcategoryName,
          source: message.source,
          type: 'new_message'
        }
      };

      // Send to all active devices
      const results = await Promise.allSettled(
        deviceTokens.map(token => this.sendPushNotification(token, notification))
      );

      const successCount = results.filter(r => r.status === 'fulfilled').length;
      const failureCount = results.filter(r => r.status === 'rejected').length;

      logger.info('Push notifications sent', {
        userId: user._id,
        successCount,
        failureCount,
        totalTokens: deviceTokens.length
      });

      // Clean up invalid tokens
      if (failureCount > 0) {
        await this.cleanupInvalidTokens(user, results, deviceTokens);
      }

    } catch (error) {
      logger.error('Error sending push notification:', error);
    }
  }

  /**
   * Create notification body text
   */
  createNotificationBody(message) {
    let body = `"${message.mainMessage}"`;
    
    if (message.mainMessage.length > 50) {
      body = `"${message.mainMessage.substring(0, 47)}..."`;
    }
    
    body += ` → ${message.categoryName}`;
    
    if (message.subcategoryName) {
      body += ` > ${message.subcategoryName}`;
    }

    return body;
  }

  /**
   * Send push notification to a specific device token
   */
  async sendPushNotification(deviceToken, notification) {
    if (!this.isProduction) {
      // Simulate push notification in development
      logger.info('Simulated push notification', {
        deviceToken: deviceToken.substring(0, 10) + '...',
        notification
      });
      return { success: true, messageId: 'simulated-' + Date.now() };
    }

    // In production, implement actual APNS integration
    try {
      // Example APNS implementation would go here
      // const apn = require('apn');
      // const provider = new apn.Provider(apnOptions);
      // const note = new apn.Notification();
      // note.alert = notification.body;
      // note.topic = process.env.APNS_BUNDLE_ID;
      // note.payload = notification.data;
      // const result = await provider.send(note, deviceToken);
      
      logger.info('Push notification sent (production)', {
        deviceToken: deviceToken.substring(0, 10) + '...'
      });
      
      return { success: true, messageId: 'prod-' + Date.now() };
    } catch (error) {
      logger.error('Failed to send push notification:', error);
      throw error;
    }
  }

  /**
   * Send sync notification to trigger app refresh
   */
  async notifySyncRequired(user, messageCount = 1) {
    try {
      const deviceTokens = user.getActiveDeviceTokens();
      
      if (deviceTokens.length === 0) {
        return;
      }

      const notification = {
        title: 'Pebl Sync',
        body: `${messageCount} new message${messageCount > 1 ? 's' : ''} ready to sync`,
        data: {
          type: 'sync_required',
          messageCount: messageCount.toString()
        }
      };

      await Promise.allSettled(
        deviceTokens.map(token => this.sendPushNotification(token, notification))
      );

      logger.info('Sync notification sent', {
        userId: user._id,
        messageCount
      });

    } catch (error) {
      logger.error('Error sending sync notification:', error);
    }
  }

  /**
   * Send batch notification for multiple messages
   */
  async notifyBatchMessages(user, messages) {
    try {
      if (messages.length === 0) return;

      const deviceTokens = user.getActiveDeviceTokens();
      
      if (deviceTokens.length === 0) {
        return;
      }

      // Group messages by category
      const categoryGroups = messages.reduce((groups, message) => {
        const key = message.categoryName;
        if (!groups[key]) groups[key] = [];
        groups[key].push(message);
        return groups;
      }, {});

      const categoryNames = Object.keys(categoryGroups);
      let body;

      if (categoryNames.length === 1) {
        const count = messages.length;
        body = `${count} new message${count > 1 ? 's' : ''} in ${categoryNames[0]}`;
      } else {
        body = `${messages.length} new messages in ${categoryNames.length} categories`;
      }

      const notification = {
        title: 'Pebl Messages',
        body: body,
        data: {
          type: 'batch_messages',
          messageCount: messages.length.toString(),
          categories: categoryNames.join(',')
        }
      };

      await Promise.allSettled(
        deviceTokens.map(token => this.sendPushNotification(token, notification))
      );

      logger.info('Batch notification sent', {
        userId: user._id,
        messageCount: messages.length,
        categories: categoryNames.length
      });

    } catch (error) {
      logger.error('Error sending batch notification:', error);
    }
  }

  /**
   * Clean up invalid device tokens
   */
  async cleanupInvalidTokens(user, results, deviceTokens) {
    try {
      const invalidTokens = [];
      
      results.forEach((result, index) => {
        if (result.status === 'rejected') {
          // Check if the error indicates an invalid token
          const error = result.reason;
          if (error.message && error.message.includes('invalid')) {
            invalidTokens.push(deviceTokens[index]);
          }
        }
      });

      if (invalidTokens.length > 0) {
        // Remove invalid tokens from user
        user.deviceTokens = user.deviceTokens.filter(
          dt => !invalidTokens.includes(dt.token)
        );
        
        await user.save();
        
        logger.info('Cleaned up invalid device tokens', {
          userId: user._id,
          removedCount: invalidTokens.length
        });
      }
    } catch (error) {
      logger.error('Error cleaning up invalid tokens:', error);
    }
  }

  /**
   * Send welcome notification for new WhatsApp integration
   */
  async notifyWhatsAppConnected(user) {
    try {
      const deviceTokens = user.getActiveDeviceTokens();
      
      if (deviceTokens.length === 0) {
        return;
      }

      const notification = {
        title: 'WhatsApp Connected! 🎉',
        body: 'You can now send messages via WhatsApp and they\'ll appear in Pebl',
        data: {
          type: 'whatsapp_connected'
        }
      };

      await Promise.allSettled(
        deviceTokens.map(token => this.sendPushNotification(token, notification))
      );

      logger.info('WhatsApp connection notification sent', {
        userId: user._id
      });

    } catch (error) {
      logger.error('Error sending WhatsApp connection notification:', error);
    }
  }
}

module.exports = new NotificationService();
