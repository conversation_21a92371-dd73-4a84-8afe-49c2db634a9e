# Pebl Backend Service

Backend service for the Pebl iOS app with WhatsApp Business API integration.

## Features

- **WhatsApp Business API Integration**: Receive and process messages from WhatsApp
- **AI-Powered Categorization**: Automatically categorize messages using OpenAI GPT-4
- **Push Notifications**: Notify iOS app when new messages arrive
- **Message Sync**: Sync messages between WhatsApp and iOS app
- **User Management**: Handle user accounts and WhatsApp number linking
- **Hashtag Support**: Parse hashtag-based category assignments (#category/subcategory)

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   WhatsApp      │    │   Pebl Backend  │    │   iOS App       │
│   Business API  │───▶│   Service       │───▶│   (Pebl)        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │   MongoDB       │
                       │   Database      │
                       └─────────────────┘
```

## Setup

### Prerequisites

- Node.js 18+ 
- MongoDB
- WhatsApp Business API account
- OpenAI API key
- Apple Developer account (for push notifications)

### Installation

1. Clone the repository and navigate to backend directory:
```bash
cd backend
```

2. Install dependencies:
```bash
npm install
```

3. Copy environment variables:
```bash
cp .env.example .env
```

4. Configure environment variables in `.env`:
```bash
# Server Configuration
PORT=3000
NODE_ENV=development

# WhatsApp Business API
WHATSAPP_ACCESS_TOKEN=your_access_token
WHATSAPP_PHONE_NUMBER_ID=your_phone_number_id
WHATSAPP_BUSINESS_ACCOUNT_ID=your_business_account_id
WHATSAPP_WEBHOOK_VERIFY_TOKEN=your_webhook_verify_token

# OpenAI API
OPENAI_API_KEY=your_openai_api_key

# Database
MONGODB_URI=mongodb://localhost:27017/pebl-backend

# JWT
JWT_SECRET=your_jwt_secret
JWT_EXPIRES_IN=7d
```

5. Start MongoDB (if running locally):
```bash
mongod
```

6. Start the development server:
```bash
npm run dev
```

## WhatsApp Business API Setup

### 1. Create WhatsApp Business Account

1. Go to [Facebook Developers](https://developers.facebook.com/)
2. Create a new app and add WhatsApp Business API
3. Get your access token and phone number ID

### 2. Configure Webhook

1. Set webhook URL: `https://your-domain.com/api/whatsapp/webhook`
2. Set verify token (same as `WHATSAPP_WEBHOOK_VERIFY_TOKEN` in .env)
3. Subscribe to `messages` webhook field

### 3. Test Integration

Send a test message to your WhatsApp Business number to verify the integration.

## API Endpoints

### Authentication
- `POST /api/auth/register` - Register/login user
- `POST /api/auth/device-token` - Update device token
- `POST /api/auth/verify` - Verify JWT token

### WhatsApp Integration
- `GET /api/whatsapp/webhook` - Webhook verification
- `POST /api/whatsapp/webhook` - Receive WhatsApp messages
- `POST /api/whatsapp/link-account` - Link WhatsApp number to user
- `POST /api/whatsapp/verify-number` - Verify WhatsApp number
- `GET /api/whatsapp/status/:userId` - Get integration status

### Messages
- `GET /api/messages/user/:userId` - Get user messages
- `GET /api/messages/user/:userId/pending-sync` - Get pending sync messages
- `POST /api/messages/mark-synced` - Mark messages as synced
- `GET /api/messages/user/:userId/statistics` - Get message statistics

### Users
- `GET /api/users/:userId` - Get user profile
- `PATCH /api/users/:userId/preferences` - Update preferences

## Message Flow

1. **User sends WhatsApp message** → WhatsApp Business API
2. **Webhook receives message** → Backend processes message
3. **AI categorization** → Message categorized using OpenAI
4. **Database storage** → Message saved to MongoDB
5. **Push notification** → iOS app notified of new message
6. **App sync** → iOS app fetches and displays message

## Categorization Logic

The backend mirrors the iOS app's categorization logic:

1. **Hashtag parsing**: Check for `#category` or `#category/subcategory`
2. **AI categorization**: Use OpenAI GPT-4 for intelligent categorization
3. **Fallback logic**: Keyword-based categorization if AI fails

### Hashtag Examples
- `#shopping Buy groceries` → Shopping category
- `#work/meetings Schedule team sync` → Work > Meetings
- `Buy groceries #shopping` → Shopping category

## Development

### Running Tests
```bash
npm test
```

### Development Mode
```bash
npm run dev
```

### Production Mode
```bash
npm start
```

## Deployment

### Environment Setup
1. Set `NODE_ENV=production`
2. Configure production MongoDB URI
3. Set up SSL certificates
4. Configure APNS for push notifications

### Docker Deployment
```bash
# Build image
docker build -t pebl-backend .

# Run container
docker run -p 3000:3000 --env-file .env pebl-backend
```

## Monitoring

- Logs are written to `logs/` directory
- Health check endpoint: `GET /health`
- Error tracking via Winston logger

## Security

- Rate limiting (100 requests per 15 minutes)
- Helmet.js for security headers
- JWT token authentication
- Webhook signature verification
- Input validation and sanitization

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

MIT License
