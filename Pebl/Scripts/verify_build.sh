#!/bin/bash

# Build verification script for Pebl project
# This script checks for common build errors and provides detailed feedback

echo "🔍 Verifying Pebl project build..."

# Change to project directory
cd "/Users/<USER>/Documents/Xcode apps/Pebl"

echo "📁 Current directory: $(pwd)"

# Check for duplicate declarations
echo "🔍 Checking for duplicate declarations..."
echo "Searching for categoryBannerShow declarations:"
grep -r "static let categoryBannerShow" . --include="*.swift" || echo "✅ No duplicate categoryBannerShow found"

echo "Searching for duplicate notification names:"
grep -r "categoryBannerShow.*=" . --include="*.swift" | grep -v "AppConstants" || echo "✅ No duplicate notification declarations found"

# Check Swift syntax
echo "🔍 Checking Swift syntax..."
find . -name "*.swift" -exec echo "Checking: {}" \; -exec swiftc -parse {} \; 2>&1 | grep -E "(error:|warning:)" | head -10

# Try to build the project
echo "🔨 Attempting to build project..."
xcodebuild -scheme Pebl -destination 'platform=iOS Simulator,name=iPhone 16,OS=18.5' build 2>&1 | grep -E "(error:|warning:|BUILD FAILED|BUILD SUCCEEDED)" | head -20

echo "✅ Build verification complete!"
