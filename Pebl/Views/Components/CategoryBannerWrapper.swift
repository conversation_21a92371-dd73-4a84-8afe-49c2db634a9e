//
//  CategoryBannerWrapper.swift
//  Pebl
//
//  Created by AI Assistant on 7/28/25.
//

import SwiftUI

/// A simple wrapper that can be easily integrated into existing views to show category banners
struct CategoryBannerWrapper<Content: View>: View {
    let content: Content
    @State private var showBanner = false
    @State private var bannerInfo: CategoryBannerInfo?
    
    init(@ViewBuilder content: () -> Content) {
        self.content = content()
    }
    
    var body: some View {
        content
            .overlay(
                VStack {
                    if let bannerInfo = bannerInfo, showBanner {
                        CategoryBanner(
                            categoryName: bannerInfo.categoryName,
                            subcategoryName: bannerInfo.subcategoryName,
                            isNewCategory: bannerInfo.isNewCategory,
                            isVisible: $showBanner
                        )
                        .padding(.top, 10)
                    }
                    Spacer()
                }
            )
            .onReceive(NotificationCenter.default.publisher(for: .categoryBannerShow)) { notification in
                if let userInfo = notification.userInfo,
                   let categoryName = userInfo["categoryName"] as? String {
                    let subcategoryName = userInfo["subcategoryName"] as? String
                    let isNewCategory = userInfo["isNewCategory"] as? Bool ?? false
                    
                    self.bannerInfo = CategoryBannerInfo(
                        categoryName: categoryName,
                        subcategoryName: subcategoryName,
                        isNewCategory: isNewCategory
                    )
                    
                    withAnimation(.easeInOut(duration: 0.3)) {
                        self.showBanner = true
                    }
                    
                    // Auto-hide after configured duration
                    DispatchQueue.main.asyncAfter(deadline: .now() + AppConstants.UI.bannerDisplayDuration) {
                        withAnimation(.easeInOut(duration: 0.3)) {
                            self.showBanner = false
                        }
                    }
                }
            }
    }
}

// Note: Notification.Name.categoryBannerShow is defined in Utilities/Constants.swift

/// Helper function to show a category banner
func showCategoryBanner(categoryName: String, subcategoryName: String? = nil, isNewCategory: Bool = false) {
    NotificationCenter.default.post(
        name: .categoryBannerShow,
        object: nil,
        userInfo: [
            "categoryName": categoryName,
            "subcategoryName": subcategoryName as Any,
            "isNewCategory": isNewCategory
        ]
    )
}

#Preview {
    CategoryBannerWrapper {
        VStack {
            Text("Sample Content")
                .padding()
            
            Button("Show Banner") {
                showCategoryBanner(categoryName: "Shopping", isNewCategory: true)
            }
            .buttonStyle(.borderedProminent)
        }
    }
}
