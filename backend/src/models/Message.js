const mongoose = require('mongoose');

const messageSchema = new mongoose.Schema({
  // Message content
  text: {
    type: String,
    required: true,
    maxlength: 1000
  },
  
  // Parsed content (similar to iOS app structure)
  mainMessage: {
    type: String,
    required: true
  },
  
  subcontent: {
    type: String,
    default: ''
  },
  
  // Source information
  source: {
    type: String,
    enum: ['app', 'whatsapp'],
    required: true
  },
  
  // WhatsApp specific fields
  whatsappMessageId: {
    type: String,
    sparse: true
  },
  
  whatsappTimestamp: {
    type: Date
  },
  
  // User and categorization
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  
  // Category information
  categoryName: {
    type: String,
    required: true
  },
  
  subcategoryName: {
    type: String,
    default: ''
  },
  
  // AI categorization metadata
  aiCategorization: {
    confidence: {
      type: Number,
      min: 0,
      max: 1
    },
    suggestedCategories: [String],
    processingTime: Number, // in milliseconds
    model: String // AI model used
  },
  
  // Message status
  isCompleted: {
    type: Boolean,
    default: false
  },
  
  isExpired: {
    type: Boolean,
    default: false
  },
  
  // Sync status with iOS app
  syncStatus: {
    type: String,
    enum: ['pending', 'synced', 'failed'],
    default: 'pending'
  },
  
  lastSyncAttempt: {
    type: Date
  },
  
  syncRetryCount: {
    type: Number,
    default: 0
  },
  
  // Timestamps
  timestamp: {
    type: Date,
    default: Date.now
  },
  
  expiresAt: {
    type: Date
  },
  
  createdAt: {
    type: Date,
    default: Date.now
  },
  
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Update the updatedAt field before saving
messageSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  
  // Set expiration date based on user preferences or default
  if (!this.expiresAt && this.timestamp) {
    const expirationDays = 60; // Default, should be fetched from user preferences
    this.expiresAt = new Date(this.timestamp.getTime() + (expirationDays * 24 * 60 * 60 * 1000));
  }
  
  next();
});

// Indexes for efficient queries
messageSchema.index({ userId: 1, timestamp: -1 });
messageSchema.index({ userId: 1, categoryName: 1, timestamp: -1 });
messageSchema.index({ whatsappMessageId: 1 }, { unique: true, sparse: true });
messageSchema.index({ syncStatus: 1 });
messageSchema.index({ source: 1, timestamp: -1 });
messageSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 }); // TTL index for automatic cleanup

// Static methods
messageSchema.statics.findByUser = function(userId, options = {}) {
  const query = { userId };
  
  if (options.category) {
    query.categoryName = options.category;
  }
  
  if (options.subcategory) {
    query.subcategoryName = options.subcategory;
  }
  
  if (options.source) {
    query.source = options.source;
  }
  
  if (options.syncStatus) {
    query.syncStatus = options.syncStatus;
  }
  
  return this.find(query)
    .sort({ timestamp: -1 })
    .limit(options.limit || 100);
};

messageSchema.statics.findPendingSync = function(userId) {
  return this.find({
    userId,
    syncStatus: 'pending',
    syncRetryCount: { $lt: 3 }
  }).sort({ timestamp: -1 });
};

// Instance methods
messageSchema.methods.markSynced = function() {
  this.syncStatus = 'synced';
  this.lastSyncAttempt = new Date();
  return this.save();
};

messageSchema.methods.markSyncFailed = function() {
  this.syncStatus = 'failed';
  this.syncRetryCount += 1;
  this.lastSyncAttempt = new Date();
  return this.save();
};

messageSchema.methods.resetSync = function() {
  this.syncStatus = 'pending';
  this.syncRetryCount = 0;
  this.lastSyncAttempt = null;
  return this.save();
};

module.exports = mongoose.model('Message', messageSchema);
