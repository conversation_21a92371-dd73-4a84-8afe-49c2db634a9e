//
//  AppDelegate.swift
//  Pebl
//
//  Created by AI Assistant on 7/3/25.
//

import UIKit
import UserNotifications

class AppDelegate: NSObject, UIApplicationDelegate {
    
    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey : Any]? = nil) -> Bool {
        
        // Configure push notifications
        UNUserNotificationCenter.current().delegate = self
        
        // Request notification permissions
        UNUserNotificationCenter.current().requestAuthorization(options: [.alert, .badge, .sound]) { granted, error in
            if granted {
                print("✅ Push notification permission granted")
                DispatchQueue.main.async {
                    application.registerForRemoteNotifications()
                }
            } else {
                print("❌ Push notification permission denied: \(error?.localizedDescription ?? "Unknown error")")
            }
        }
        
        return true
    }
    
    // MARK: - Push Notification Registration
    
    func application(_ application: UIApplication, didRegisterForRemoteNotificationsWithDeviceToken deviceToken: Data) {
        let tokenParts = deviceToken.map { data in String(format: "%02.2hhx", data) }
        let token = tokenParts.joined()
        
        print("📱 Device token received: \(token)")
        
        // Update device token with UserManager
        UserManager.shared.updateDeviceToken(token)
    }
    
    func application(_ application: UIApplication, didFailToRegisterForRemoteNotificationsWithError error: Error) {
        print("❌ Failed to register for remote notifications: \(error.localizedDescription)")
    }
    
    // MARK: - Push Notification Handling
    
    func application(_ application: UIApplication, didReceiveRemoteNotification userInfo: [AnyHashable : Any], fetchCompletionHandler completionHandler: @escaping (UIBackgroundFetchResult) -> Void) {
        
        print("📨 Received remote notification: \(userInfo)")
        
        // Handle different notification types
        if let notificationType = userInfo["type"] as? String {
            switch notificationType {
            case "new_message":
                handleNewMessageNotification(userInfo)
            case "sync_required":
                handleSyncRequiredNotification(userInfo)
            case "batch_messages":
                handleBatchMessagesNotification(userInfo)
            case "whatsapp_connected":
                handleWhatsAppConnectedNotification(userInfo)
            default:
                print("🤷‍♂️ Unknown notification type: \(notificationType)")
            }
        }
        
        completionHandler(.newData)
    }
    
    // MARK: - Notification Handlers
    
    private func handleNewMessageNotification(_ userInfo: [AnyHashable: Any]) {
        guard let userId = UserManager.shared.currentUserId else { return }
        
        // Trigger sync for new message
        SyncService.shared.syncPendingMessages(for: userId, categoryManager: CategoryManager.shared)
        
        print("🔄 Triggered sync for new message notification")
    }
    
    private func handleSyncRequiredNotification(_ userInfo: [AnyHashable: Any]) {
        guard let userId = UserManager.shared.currentUserId else { return }
        
        // Trigger sync
        SyncService.shared.syncPendingMessages(for: userId, categoryManager: CategoryManager.shared)
        
        print("🔄 Triggered sync for sync required notification")
    }
    
    private func handleBatchMessagesNotification(_ userInfo: [AnyHashable: Any]) {
        guard let userId = UserManager.shared.currentUserId else { return }
        
        // Trigger sync for batch messages
        SyncService.shared.syncPendingMessages(for: userId, categoryManager: CategoryManager.shared)
        
        print("🔄 Triggered sync for batch messages notification")
    }
    
    private func handleWhatsAppConnectedNotification(_ userInfo: [AnyHashable: Any]) {
        // Refresh WhatsApp integration status
        WhatsAppIntegrationService.shared.refreshStatus()
        
        print("🎉 WhatsApp connected notification received")
    }
}

// MARK: - UNUserNotificationCenterDelegate

extension AppDelegate: UNUserNotificationCenterDelegate {
    
    // Handle notification when app is in foreground
    func userNotificationCenter(_ center: UNUserNotificationCenter, willPresent notification: UNNotification, withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void) {
        
        let userInfo = notification.request.content.userInfo
        print("📨 Received notification while app is in foreground: \(userInfo)")
        
        // Handle the notification
        if let notificationType = userInfo["type"] as? String {
            switch notificationType {
            case "new_message", "sync_required", "batch_messages":
                // For message notifications, show banner and trigger sync
                handleNewMessageNotification(userInfo)
                completionHandler([.banner, .sound])
            case "whatsapp_connected":
                // For WhatsApp connection, show banner
                handleWhatsAppConnectedNotification(userInfo)
                completionHandler([.banner, .sound])
            default:
                completionHandler([.banner])
            }
        } else {
            completionHandler([.banner])
        }
    }
    
    // Handle notification tap
    func userNotificationCenter(_ center: UNUserNotificationCenter, didReceive response: UNNotificationResponse, withCompletionHandler completionHandler: @escaping () -> Void) {
        
        let userInfo = response.notification.request.content.userInfo
        print("👆 User tapped notification: \(userInfo)")
        
        // Handle notification tap based on type
        if let notificationType = userInfo["type"] as? String {
            switch notificationType {
            case "new_message":
                handleNewMessageTap(userInfo)
            case "sync_required", "batch_messages":
                handleSyncTap(userInfo)
            case "whatsapp_connected":
                handleWhatsAppConnectedTap(userInfo)
            default:
                break
            }
        }
        
        completionHandler()
    }
    
    // MARK: - Notification Tap Handlers
    
    private func handleNewMessageTap(_ userInfo: [AnyHashable: Any]) {
        // Navigate to the specific category if available
        if let categoryName = userInfo["categoryName"] as? String {
            // Post notification to navigate to category
            NotificationCenter.default.post(
                name: NSNotification.Name("NavigateToCategory"),
                object: nil,
                userInfo: ["categoryName": categoryName]
            )
        }
    }
    
    private func handleSyncTap(_ userInfo: [AnyHashable: Any]) {
        // Trigger immediate sync
        guard let userId = UserManager.shared.currentUserId else { return }
        SyncService.shared.syncPendingMessages(for: userId, categoryManager: CategoryManager.shared)
    }
    
    private func handleWhatsAppConnectedTap(_ userInfo: [AnyHashable: Any]) {
        // Navigate to WhatsApp settings
        NotificationCenter.default.post(
            name: NSNotification.Name("NavigateToWhatsAppSettings"),
            object: nil
        )
    }
}
