//
//  CategoryManager.swift
//  Pebl
//
//  Created by <PERSON><PERSON> on 6/17/25.
//

import Foundation

/// Manages the root categories and provides utility functions
class CategoryManager: ObservableObject {

    // MARK: - Singleton

    static let shared = CategoryManager()

    @Published var rootCategories: [Category] = []

    // Centralized categorization coordinator
    private(set) lazy var categorizationCoordinator = CategorizationCoordinator(categoryManager: self)

    init() {
        loadDefaultCategories()
    }
    
    internal func loadDefaultCategories() {
        rootCategories = DefaultCategoriesConfig.defaultCategories.map { categoryInfo in
            Category(name: categoryInfo.name, sfSymbol: categoryInfo.sfSymbol, categoryManager: self)
        }
    }
    
    // MARK: - Category Management
    
    /// Add a new root category
    func addRootCategory(name: String, sfSymbol: String = "folder.fill") -> Category {
        let newCategory = Category(name: name, sfSymbol: sfSymbol, categoryManager: self)
        rootCategories.append(newCategory)
        return newCategory
    }
    
    /// Remove a root category
    func removeRootCategory(_ category: Category) {
        rootCategories.removeAll { $0.id == category.id }
    }
    
    /// Find any category by name across all root categories
    func findCategory(named name: String) -> Category? {
        for rootCategory in rootCategories {
            if let found = rootCategory.findCategory(named: name) {
                return found
            }
        }
        return nil
    }
    
    /// Get all category names for AI categorization
    func getAllCategoryNames() -> [String] {
        return rootCategories.flatMap { $0.getAllCategoryNames() }
    }
    
    // MARK: - Message Management
    
    /// Categorize a message using AI and add it to the appropriate category (async)
    /// DEPRECATED: Use categorizationCoordinator.addMessage instead
    func categorizeAndAddMessage(_ message: String, using aiModel: AIModel, completion: @escaping () -> Void) {
        // Delegate to the new centralized system
        categorizationCoordinator.addMessage(message) { success in
            completion()
        }
    }

    /// Modern interface: Add a message using the centralized categorization system
    func addMessage(_ message: String, completion: @escaping (Bool) -> Void) {
        categorizationCoordinator.addMessage(message, completion: completion)
    }

    /// Modern interface: Add a message with detailed categorization result
    func addMessageWithDetails(_ message: String, completion: @escaping (CategorizationAddResult) -> Void) {
        categorizationCoordinator.addMessageWithDetails(message) { result in
            // Show banner notification if successful
            if result.success, let categoryName = result.categoryName {
                DispatchQueue.main.async {
                    NotificationCenter.default.post(
                        name: .categoryBannerShow,
                        object: nil,
                        userInfo: [
                            "categoryName": categoryName,
                            "subcategoryName": result.subcategoryName as Any,
                            "isNewCategory": result.isNewCategory
                        ]
                    )
                }
            }
            completion(result)
        }
    }

    /// Batch add multiple messages using the centralized system
    func batchAddMessages(_ messages: [String], completion: @escaping (BatchAddResult) -> Void) {
        categorizationCoordinator.batchAddMessages(messages, completion: completion)
    }

    // MARK: - Message Expiration Management

    /// Remove expired messages from all categories based on app settings
    func removeExpiredMessages(using settings: AppSettings) {
        for category in rootCategories {
            category.removeExpiredMessages(using: settings)
        }
    }

    /// Get total count of expired messages across all categories
    func getTotalExpiredMessageCount(using settings: AppSettings) -> Int {
        return rootCategories.reduce(0) { $0 + $1.getExpiredMessageCount(using: settings) }
    }

    /// Get all expired messages across all categories
    func getAllExpiredMessages(using settings: AppSettings) -> [Message] {
        var allExpiredMessages: [Message] = []
        for category in rootCategories {
            allExpiredMessages.append(contentsOf: category.getExpiredMessages(using: settings))
        }
        return allExpiredMessages
    }

    /// Perform automatic cleanup of expired messages if enabled in settings
    func performAutomaticCleanupIfNeeded(using settingsManager: SettingsManager) {
        guard settingsManager.settings.autoDeleteExpiredMessages else { return }

        let expiredCount = getTotalExpiredMessageCount(using: settingsManager.settings)
        if expiredCount > 0 {
            print("Performing automatic cleanup of \(expiredCount) expired messages")
            removeExpiredMessages(using: settingsManager.settings)
        }
    }

    // MARK: - Persistence

    /// Save categories to file storage
    func saveToFile() {
        do {
            try CategoryStorageService.shared.saveCategories(from: self)
        } catch {
            print("Error saving categories: \(error)")
        }
    }

    /// Load categories from file storage
    func loadFromFile() {
        CategoryStorageService.shared.loadCategoriesWithFallback(into: self)
    }
}
