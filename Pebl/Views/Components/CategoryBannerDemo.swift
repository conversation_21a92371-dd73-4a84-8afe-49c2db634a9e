//
//  CategoryBannerDemo.swift
//  Pebl
//
//  Created by AI Assistant on 7/28/25.
//

import SwiftUI

/// Demo view to showcase the CategoryBanner functionality
struct CategoryBannerDemo: View {
    @State private var showBanner1 = false
    @State private var showBanner2 = false
    @State private var showBanner3 = false
    
    var body: some View {
        VStack(spacing: 20) {
            Text("Category Banner Demo")
                .font(.title)
                .padding()
            
            VStack(spacing: 15) {
                <PERSON><PERSON>("Show New Category Banner") {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        showBanner1 = true
                    }
                    
                    // Auto-hide after 3 seconds
                    DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
                        withAnimation(.easeInOut(duration: 0.3)) {
                            showBanner1 = false
                        }
                    }
                }
                .buttonStyle(.borderedProminent)
                
                <PERSON><PERSON>("Show Existing Category Banner") {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        showBanner2 = true
                    }
                    
                    // Auto-hide after 3 seconds
                    DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
                        withAnimation(.easeInOut(duration: 0.3)) {
                            showBanner2 = false
                        }
                    }
                }
                .buttonStyle(.bordered)
                
                <PERSON><PERSON>("Show Subcategory Banner") {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        showBanner3 = true
                    }
                    
                    // Auto-hide after 3 seconds
                    DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
                        withAnimation(.easeInOut(duration: 0.3)) {
                            showBanner3 = false
                        }
                    }
                }
                .buttonStyle(.bordered)
            }
            
            Spacer()
        }
        .overlay(
            // Banner overlays
            VStack {
                if showBanner1 {
                    CategoryBanner(
                        categoryName: "Shopping",
                        subcategoryName: nil,
                        isNewCategory: true,
                        isVisible: $showBanner1
                    )
                    .padding(.top, 10)
                }
                
                if showBanner2 {
                    CategoryBanner(
                        categoryName: "Movies to Watch",
                        subcategoryName: nil,
                        isNewCategory: false,
                        isVisible: $showBanner2
                    )
                    .padding(.top, 10)
                }
                
                if showBanner3 {
                    CategoryBanner(
                        categoryName: "Movies to Watch",
                        subcategoryName: "Comedy",
                        isNewCategory: false,
                        isVisible: $showBanner3
                    )
                    .padding(.top, 10)
                }
                
                Spacer()
            }
        )
    }
}

#Preview {
    CategoryBannerDemo()
}
