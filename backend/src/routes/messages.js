const express = require('express');
const MessageService = require('../services/MessageService');
const logger = require('../utils/logger');

const router = express.Router();

// Get messages for a user
router.get('/user/:userId', async (req, res) => {
  try {
    const { userId } = req.params;
    const { 
      category, 
      subcategory, 
      source, 
      syncStatus, 
      limit = 100,
      offset = 0 
    } = req.query;

    const options = {
      category,
      subcategory,
      source,
      syncStatus,
      limit: parseInt(limit),
      offset: parseInt(offset)
    };

    const messages = await MessageService.getMessagesForUser(userId, options);

    res.status(200).json({
      success: true,
      messages,
      count: messages.length
    });

  } catch (error) {
    logger.error('Error getting messages:', error);
    res.status(500).json({
      error: 'Failed to get messages',
      details: error.message
    });
  }
});

// Get pending sync messages for a user
router.get('/user/:userId/pending-sync', async (req, res) => {
  try {
    const { userId } = req.params;
    const messages = await MessageService.getPendingSyncMessages(userId);

    res.status(200).json({
      success: true,
      messages,
      count: messages.length
    });

  } catch (error) {
    logger.error('Error getting pending sync messages:', error);
    res.status(500).json({
      error: 'Failed to get pending sync messages',
      details: error.message
    });
  }
});

// Mark messages as synced
router.post('/mark-synced', async (req, res) => {
  try {
    const { messageIds } = req.body;
    
    if (!messageIds || !Array.isArray(messageIds)) {
      return res.status(400).json({
        error: 'messageIds array is required'
      });
    }

    const result = await MessageService.markMessagesSynced(messageIds);

    res.status(200).json({
      success: true,
      modifiedCount: result.modifiedCount
    });

  } catch (error) {
    logger.error('Error marking messages as synced:', error);
    res.status(500).json({
      error: 'Failed to mark messages as synced',
      details: error.message
    });
  }
});

// Retry failed sync messages
router.post('/user/:userId/retry-sync', async (req, res) => {
  try {
    const { userId } = req.params;
    const messages = await MessageService.retryFailedSyncMessages(userId);

    res.status(200).json({
      success: true,
      retriedCount: messages.length,
      messages
    });

  } catch (error) {
    logger.error('Error retrying failed sync messages:', error);
    res.status(500).json({
      error: 'Failed to retry sync messages',
      details: error.message
    });
  }
});

// Delete expired messages
router.delete('/user/:userId/expired', async (req, res) => {
  try {
    const { userId } = req.params;
    const result = await MessageService.deleteExpiredMessages(userId);

    res.status(200).json({
      success: true,
      deletedCount: result.deletedCount
    });

  } catch (error) {
    logger.error('Error deleting expired messages:', error);
    res.status(500).json({
      error: 'Failed to delete expired messages',
      details: error.message
    });
  }
});

// Get message statistics
router.get('/user/:userId/statistics', async (req, res) => {
  try {
    const { userId } = req.params;
    const stats = await MessageService.getMessageStatistics(userId);

    res.status(200).json({
      success: true,
      statistics: stats
    });

  } catch (error) {
    logger.error('Error getting message statistics:', error);
    res.status(500).json({
      error: 'Failed to get message statistics',
      details: error.message
    });
  }
});

module.exports = router;
