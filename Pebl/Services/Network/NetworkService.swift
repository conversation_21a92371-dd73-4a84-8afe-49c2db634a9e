//
//  NetworkService.swift
//  Pebl
//
//  Created by AI Assistant on 7/3/25.
//

import Foundation
import Combine

/// Network service for communicating with the Pebl backend
class NetworkService: ObservableObject {
    
    // MARK: - Singleton
    
    static let shared = NetworkService()
    
    private init() {}
    
    // MARK: - Properties
    
    private let baseURL = AppConfig.backendBaseURL
    private let session = URLSession.shared
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Authentication
    
    /// Register or login user with the backend
    func registerUser(email: String, deviceToken: String? = nil) -> AnyPublisher<AuthResponse, NetworkError> {
        let endpoint = "/api/auth/register"
        let body = RegisterRequest(email: email, deviceToken: deviceToken)

        return makeRequest(endpoint: endpoint, method: .POST, body: body)
    }

    /// Register or login user with phone number
    func registerUser(phoneNumber: String, deviceToken: String? = nil) -> AnyPublisher<AuthResponse, NetworkError> {
        let endpoint = "/api/auth/register"
        let body = RegisterPhoneRequest(phoneNumber: phoneNumber, deviceToken: deviceToken)

        return makeRequest(endpoint: endpoint, method: .POST, body: body)
    }
    
    /// Update device token for push notifications
    func updateDeviceToken(userId: String, deviceToken: String) -> AnyPublisher<BasicResponse, NetworkError> {
        let endpoint = "/api/auth/device-token"
        let body = DeviceTokenRequest(userId: userId, deviceToken: deviceToken)
        
        return makeRequest(endpoint: endpoint, method: .POST, body: body)
    }
    
    // MARK: - WhatsApp Integration
    
    /// Link WhatsApp number to user account
    func linkWhatsAppAccount(userId: String, whatsappNumber: String) -> AnyPublisher<LinkAccountResponse, NetworkError> {
        let endpoint = "/api/whatsapp/link-account"
        let body = LinkAccountRequest(userId: userId, whatsappNumber: whatsappNumber)
        
        return makeRequest(endpoint: endpoint, method: .POST, body: body)
    }
    
    /// Verify WhatsApp number with verification code
    func verifyWhatsAppNumber(userId: String, verificationCode: String) -> AnyPublisher<VerifyNumberResponse, NetworkError> {
        let endpoint = "/api/whatsapp/verify-number"
        let body = VerifyNumberRequest(userId: userId, verificationCode: verificationCode)
        
        return makeRequest(endpoint: endpoint, method: .POST, body: body)
    }
    
    /// Get WhatsApp integration status
    func getWhatsAppStatus(userId: String) -> AnyPublisher<WhatsAppStatusResponse, NetworkError> {
        let endpoint = "/api/whatsapp/status/\(userId)"
        
        return makeRequest(endpoint: endpoint, method: .GET)
    }
    
    // MARK: - Message Sync
    
    /// Get pending sync messages from backend
    func getPendingSyncMessages(userId: String) -> AnyPublisher<MessagesResponse, NetworkError> {
        let endpoint = "/api/messages/user/\(userId)/pending-sync"
        
        return makeRequest(endpoint: endpoint, method: .GET)
    }
    
    /// Mark messages as synced
    func markMessagesSynced(messageIds: [String]) -> AnyPublisher<BasicResponse, NetworkError> {
        let endpoint = "/api/messages/mark-synced"
        let body = MarkSyncedRequest(messageIds: messageIds)
        
        return makeRequest(endpoint: endpoint, method: .POST, body: body)
    }
    
    /// Get all messages for a user
    func getMessages(userId: String, category: String? = nil, limit: Int = 100) -> AnyPublisher<MessagesResponse, NetworkError> {
        var endpoint = "/api/messages/user/\(userId)?limit=\(limit)"
        
        if let category = category {
            endpoint += "&category=\(category.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? category)"
        }
        
        return makeRequest(endpoint: endpoint, method: .GET)
    }
    
    /// Get message statistics
    func getMessageStatistics(userId: String) -> AnyPublisher<MessageStatisticsResponse, NetworkError> {
        let endpoint = "/api/messages/user/\(userId)/statistics"
        
        return makeRequest(endpoint: endpoint, method: .GET)
    }
    
    // MARK: - User Management
    
    /// Update user preferences
    func updateUserPreferences(userId: String, preferences: UserPreferences) -> AnyPublisher<BasicResponse, NetworkError> {
        let endpoint = "/api/users/\(userId)/preferences"
        let body = UpdatePreferencesRequest(preferences: preferences)
        
        return makeRequest(endpoint: endpoint, method: .PATCH, body: body)
    }
    
    /// Update last sync timestamp
    func updateSyncTimestamp(userId: String) -> AnyPublisher<BasicResponse, NetworkError> {
        let endpoint = "/api/users/\(userId)/sync-timestamp"
        
        return makeRequest(endpoint: endpoint, method: .PATCH, body: EmptyBody())
    }
    
    // MARK: - Generic Request Method
    
    private func makeRequest<T: Codable, B: Codable>(
        endpoint: String,
        method: HTTPMethod,
        body: B? = nil
    ) -> AnyPublisher<T, NetworkError> {
        
        guard let url = URL(string: baseURL + endpoint) else {
            return Fail(error: NetworkError.invalidURL)
                .eraseToAnyPublisher()
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = method.rawValue
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        // Add body if provided
        if let body = body {
            do {
                request.httpBody = try JSONEncoder().encode(body)
            } catch {
                return Fail(error: NetworkError.encodingError(error))
                    .eraseToAnyPublisher()
            }
        }
        
        return session.dataTaskPublisher(for: request)
            .map(\.data)
            .decode(type: T.self, decoder: JSONDecoder())
            .mapError { error in
                if error is DecodingError {
                    return NetworkError.decodingError(error)
                } else {
                    return NetworkError.networkError(error)
                }
            }
            .eraseToAnyPublisher()
    }
    
    private func makeRequest<T: Codable>(
        endpoint: String,
        method: HTTPMethod
    ) -> AnyPublisher<T, NetworkError> {
        return makeRequest(endpoint: endpoint, method: method, body: EmptyBody?.none)
    }
}

// MARK: - HTTP Method

enum HTTPMethod: String {
    case GET = "GET"
    case POST = "POST"
    case PATCH = "PATCH"
    case DELETE = "DELETE"
}

// MARK: - Network Error

enum NetworkError: Error, LocalizedError {
    case invalidURL
    case networkError(Error)
    case encodingError(Error)
    case decodingError(Error)
    case serverError(String)
    
    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "Invalid URL"
        case .networkError(let error):
            return "Network error: \(error.localizedDescription)"
        case .encodingError(let error):
            return "Encoding error: \(error.localizedDescription)"
        case .decodingError(let error):
            return "Decoding error: \(error.localizedDescription)"
        case .serverError(let message):
            return "Server error: \(message)"
        }
    }
}

// MARK: - Empty Body

private struct EmptyBody: Codable {}
