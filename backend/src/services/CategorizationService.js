const axios = require('axios');
const logger = require('../utils/logger');

class CategorizationService {
  constructor() {
    this.openaiApiKey = process.env.OPENAI_API_KEY;
    this.baseURL = 'https://api.openai.com/v1/chat/completions';
  }

  /**
   * Categorize a message using AI (mirrors iOS app logic)
   */
  async categorizeMessage(messageText, userId) {
    const startTime = Date.now();
    
    try {
      // Step 1: Parse the message for hashtags first
      const hashtagResult = this.parseHashtagMessage(messageText);
      
      if (hashtagResult.categoryPath) {
        // Handle hashtag-specified categorization
        return {
          categoryName: hashtagResult.categoryPath.category,
          subcategoryName: hashtagResult.categoryPath.subcategory || '',
          parsedMessage: this.parseMessageContent(hashtagResult.cleanedMessage),
          confidence: 1.0, // High confidence for explicit hashtags
          suggestedCategories: [hashtagResult.categoryPath.category],
          processingTime: Date.now() - startTime,
          model: 'hashtag-parser'
        };
      }

      // Step 2: Parse message content
      const parsedMessage = this.parseMessageContent(messageText);

      // Step 3: Get available categories for this user (simplified for backend)
      const availableCategories = await this.getAvailableCategories(userId);

      // Step 4: Use AI for categorization
      const aiResult = await this.performAICategorization(messageText, availableCategories);

      return {
        categoryName: aiResult.categoryName,
        subcategoryName: aiResult.subcategoryName || '',
        parsedMessage: parsedMessage,
        confidence: aiResult.confidence,
        suggestedCategories: aiResult.suggestedCategories,
        processingTime: Date.now() - startTime,
        model: 'gpt-4'
      };

    } catch (error) {
      logger.error('Error in categorization:', error);
      
      // Fallback categorization
      return {
        categoryName: 'Uncategorized',
        subcategoryName: '',
        parsedMessage: this.parseMessageContent(messageText),
        confidence: 0.1,
        suggestedCategories: ['Uncategorized'],
        processingTime: Date.now() - startTime,
        model: 'fallback'
      };
    }
  }

  /**
   * Parse hashtag-based category specification (mirrors iOS HashtagParser)
   */
  parseHashtagMessage(messageText) {
    // Look for hashtags at the beginning or end of the message
    const hashtagRegex = /#([a-zA-Z0-9_-]+(?:\/[a-zA-Z0-9_-]+)?)/g;
    const matches = messageText.match(hashtagRegex);
    
    if (!matches || matches.length === 0) {
      return {
        categoryPath: null,
        cleanedMessage: messageText,
        originalMessage: messageText
      };
    }

    // Use the first hashtag found
    const hashtag = matches[0];
    const hashtagContent = hashtag.substring(1); // Remove #
    
    // Parse category/subcategory
    const parts = hashtagContent.split('/');
    const categoryPath = {
      category: this.normalizeCategory(parts[0]),
      subcategory: parts.length > 1 ? this.normalizeCategory(parts[1]) : null
    };

    // Clean the message by removing the hashtag
    const cleanedMessage = messageText.replace(hashtag, '').trim();

    return {
      categoryPath,
      cleanedMessage,
      originalMessage: messageText
    };
  }

  /**
   * Normalize category names (case-insensitive, handle special characters)
   */
  normalizeCategory(categoryName) {
    return categoryName
      .toLowerCase()
      .replace(/[_-]/g, ' ')
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join('-');
  }

  /**
   * Parse message content to extract main message and subcontent
   */
  parseMessageContent(messageText) {
    // Check if it's a URL
    if (this.isURL(messageText)) {
      return this.parseURLContent(messageText);
    }

    // For regular messages, the entire text is the main message
    return {
      mainMessage: messageText.trim(),
      subcontent: ''
    };
  }

  /**
   * Check if text is a URL
   */
  isURL(text) {
    return text.toLowerCase().startsWith('http://') || 
           text.toLowerCase().startsWith('https://');
  }

  /**
   * Parse URL content (simplified version of iOS URLContentAnalyzer)
   */
  parseURLContent(url) {
    try {
      const urlObj = new URL(url);
      const domain = urlObj.hostname;
      
      // Extract meaningful information from common domains
      let mainMessage = url;
      let subcontent = domain;

      // YouTube
      if (domain.includes('youtube.com') || domain.includes('youtu.be')) {
        mainMessage = 'YouTube Video';
        subcontent = url;
      }
      // Amazon
      else if (domain.includes('amazon.com')) {
        mainMessage = 'Amazon Product';
        subcontent = url;
      }
      // Netflix
      else if (domain.includes('netflix.com')) {
        mainMessage = 'Netflix Content';
        subcontent = url;
      }
      // GitHub
      else if (domain.includes('github.com')) {
        mainMessage = 'GitHub Repository';
        subcontent = url;
      }
      // Default
      else {
        mainMessage = `Link: ${domain}`;
        subcontent = url;
      }

      return { mainMessage, subcontent };
    } catch (error) {
      return {
        mainMessage: url,
        subcontent: ''
      };
    }
  }

  /**
   * Get available categories for a user (simplified - in production, fetch from user's data)
   */
  async getAvailableCategories(userId) {
    // For now, return default categories
    // In production, this should fetch the user's existing categories
    return [
      'To-Do', 'Shopping', 'Entertainment', 'Work', 'Learning', 
      'Health', 'Travel', 'Food', 'Technology', 'Lifestyle',
      'Books', 'Movies', 'Music', 'Sports', 'Finance'
    ];
  }

  /**
   * Perform AI categorization using OpenAI
   */
  async performAICategorization(messageText, availableCategories) {
    if (!this.openaiApiKey) {
      logger.warn('OpenAI API key not available, using fallback categorization');
      return this.fallbackCategorization(messageText, availableCategories);
    }

    try {
      const prompt = this.buildCategorizationPrompt(messageText, availableCategories);
      const response = await this.makeOpenAIRequest(prompt);
      
      return this.parseAIResponse(response, availableCategories, messageText);
    } catch (error) {
      logger.error('AI categorization failed:', error);
      return this.fallbackCategorization(messageText, availableCategories);
    }
  }

  /**
   * Build categorization prompt for AI
   */
  buildCategorizationPrompt(messageText, availableCategories) {
    return `Categorize this message into one of the available categories. If none fit well, suggest a new category.

Message: "${messageText}"

Available categories: ${availableCategories.join(', ')}

Rules:
1. Choose the most appropriate existing category if possible
2. If no existing category fits well, suggest a new category name
3. Keep category names concise (1-2 words)
4. Consider the intent and context of the message

Respond with ONLY the category name, nothing else.`;
  }

  /**
   * Make request to OpenAI API
   */
  async makeOpenAIRequest(prompt) {
    const response = await axios.post(this.baseURL, {
      model: 'gpt-4',
      messages: [
        {
          role: 'system',
          content: 'You are a precise categorization assistant. Always respond with ONLY the exact category name. Never provide explanations or additional text.'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      max_tokens: 20,
      temperature: 0.1
    }, {
      headers: {
        'Authorization': `Bearer ${this.openaiApiKey}`,
        'Content-Type': 'application/json'
      },
      timeout: 15000
    });

    return response.data.choices[0].message.content.trim();
  }

  /**
   * Parse AI response and validate
   */
  parseAIResponse(response, availableCategories, messageText) {
    const categoryName = response.trim();
    
    // Check if it's an existing category
    const existingCategory = availableCategories.find(cat => 
      cat.toLowerCase() === categoryName.toLowerCase()
    );

    if (existingCategory) {
      return {
        categoryName: existingCategory,
        subcategoryName: null,
        confidence: 0.9,
        suggestedCategories: [existingCategory]
      };
    }

    // Validate if it's a reasonable new category
    if (this.isValidNewCategory(categoryName, messageText)) {
      return {
        categoryName: categoryName,
        subcategoryName: null,
        confidence: 0.8,
        suggestedCategories: [categoryName]
      };
    }

    // Fallback to best existing category
    return this.fallbackCategorization(messageText, availableCategories);
  }

  /**
   * Check if a new category name is valid
   */
  isValidNewCategory(categoryName, messageText) {
    // Basic validation rules
    if (!categoryName || categoryName.length < 2 || categoryName.length > 20) {
      return false;
    }

    // Check for invalid patterns
    const invalidPatterns = [
      /^\d+$/, // Only numbers
      /^[^a-zA-Z]+$/, // No letters
      /error|invalid|unknown/i // Error-like responses
    ];

    return !invalidPatterns.some(pattern => pattern.test(categoryName));
  }

  /**
   * Fallback categorization when AI fails
   */
  fallbackCategorization(messageText, availableCategories) {
    // Simple keyword-based categorization
    const keywords = {
      'Shopping': ['buy', 'purchase', 'order', 'shop', 'store'],
      'To-Do': ['do', 'task', 'complete', 'finish', 'remember'],
      'Work': ['meeting', 'project', 'deadline', 'office', 'work'],
      'Entertainment': ['watch', 'movie', 'show', 'game', 'fun'],
      'Learning': ['learn', 'study', 'read', 'course', 'book']
    };

    const lowerMessage = messageText.toLowerCase();
    
    for (const [category, words] of Object.entries(keywords)) {
      if (availableCategories.includes(category) && 
          words.some(word => lowerMessage.includes(word))) {
        return {
          categoryName: category,
          subcategoryName: null,
          confidence: 0.6,
          suggestedCategories: [category]
        };
      }
    }

    // Default fallback
    return {
      categoryName: availableCategories.includes('To-Do') ? 'To-Do' : availableCategories[0],
      subcategoryName: null,
      confidence: 0.3,
      suggestedCategories: ['To-Do', 'Uncategorized']
    };
  }
}

module.exports = new CategorizationService();
