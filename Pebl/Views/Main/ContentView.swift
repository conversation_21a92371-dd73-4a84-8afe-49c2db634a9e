//
//  ContentView.swift
//  Pebl
//
//  Created by <PERSON><PERSON> on 6/4/25.
//

import SwiftUI
import UIKit


struct ContentView: View {
    @EnvironmentObject var categoryManager: CategoryManager
    @EnvironmentObject var settingsManager: SettingsManager
    @EnvironmentObject var userManager: UserManager
    @State private var selectedCategory: Category? = nil
    @State private var navigationStack: [Category] = []
    @State private var isSearching: Bool = false
    @State private var searchText: String = ""
    @State private var showingSettings: Bool = false
    @State private var inputMessage: String = ""

    var body: some View {
        CategoryBannerWrapper {
            NavigationView {
            VStack {
                if let selectedCategory = selectedCategory {
                    CategoryDetailView(
                        category: selectedCategory,
                        categoryManager: categoryManager,
                        onBack: {
                            if navigationStack.isEmpty {
                                self.selectedCategory = nil
                            } else {
                                self.selectedCategory = navigationStack.removeLast()
                            }
                        },
                        onNavigateToSubcategory: { subcategory in
                            navigationStack.append(selectedCategory)
                            self.selectedCategory = subcategory
                        }
                    )
                } else {
                    // Main screen
                    VStack(spacing: 5) {
                        // Input mode toggle
                        HStack {
                            Picker("Mode", selection: $isSearching) {
                                Text("Store").tag(false)
                                Text("Search").tag(true)
                            }
                            .pickerStyle(SegmentedPickerStyle())
                            .padding(.horizontal)
                        }
                        .padding(.top, 10)
                        
                        // Combined text field for both search and store
                        HStack {
                            TextField(isSearching ? "Search messages..." : "Enter your message here...", text: isSearching ? $searchText : $inputMessage)
                                .textFieldStyle(RoundedBorderTextFieldStyle())
                                .padding(.horizontal)
                            
                            if isSearching {
                                Button(action: {
                                    // Clear search
                                    searchText = ""
                                }) {
                                    Image(systemName: "xmark.circle.fill")
                                        .foregroundColor(.gray)
                                }
                                .padding(.trailing)
                                .opacity(searchText.isEmpty ? 0 : 1)
                            } else {
                                Button("Store") {
                                    categorizeMessage(inputMessage)
                                    inputMessage = ""
                                }
                                .font(.headline)
                                .padding()
                                .frame(height: 40)
                                .background(Color.white)
                                .foregroundColor(.black)
                                .cornerRadius(10)
                                .shadow(color: Color.gray.opacity(0.5), radius: 5, x: 0, y: 2)
                                .padding(.trailing)
                            }
                        }
                        .padding(.bottom, 20)
                        
                        // Search results or categories display
                        if isSearching {
                            // Search results
                            SearchResultsListView(
                                searchText: searchText,
                                categoryManager: categoryManager,
                                onSelectResult: { category in
                                    self.selectedCategory = category
                                    isSearching = false
                                }
                            )
                        } else {
                            // Categories grid (existing code)
                            ScrollView {
                                LazyVGrid(columns: [GridItem(.flexible()), GridItem(.flexible())]) {
                                    ForEach(categoryManager.rootCategories.sorted(by: { $0.name < $1.name }), id: \.id) { category in
                                        Button(action: {
                                            self.selectedCategory = category
                                        }) {
                                            VStack {
                                                Image(systemName: category.sfSymbol.isEmpty ? "folder.fill" : category.sfSymbol)
                                                    .resizable()
                                                    .scaledToFit()
                                                    .frame(width: 120, height: 40)
                                                    .foregroundColor(.black)
                                                HStack {
                                                    Text(category.name)
                                                        .foregroundColor(.black)
                                                    Text("(\(category.getTotalMessageCount()))")
                                                        .font(.caption)
                                                        .foregroundColor(.gray)
                                                }
                                            }
                                            .frame(width: 180, height: 120)
                                            .background(Color.white)
                                            .cornerRadius(10)
                                            .shadow(color: Color.gray.opacity(0.5), radius: 5, x: 0, y: 5)
                                        }
                                        .dropDestination(for: Message.self) { messages, location in
                                            // Handle dropped messages
                                            for message in messages {
                                                moveMessageToCategory(message, targetCategory: category)
                                            }
                                            return true
                                        }
                                    }
                                }
                                .padding(.bottom, 20)

                                Button("Add Category") {
                                    let alert = UIAlertController(title: "New Category", message: "Enter a name for the new category", preferredStyle: .alert)
                                    alert.addTextField { textField in
                                        textField.placeholder = "Category name"
                                    }
                                    alert.addAction(UIAlertAction(title: "Add", style: .default) { _ in
                                        if let categoryName = alert.textFields?.first?.text, !categoryName.isEmpty {
                                            DispatchQueue.main.async {
                                                addNewCategory(categoryName)
                                            }
                                        }
                                    })
                                    alert.addAction(UIAlertAction(title: "Cancel", style: .cancel, handler: nil))

                                    if let scene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                                       let window = scene.windows.first,
                                       let rootVC = window.rootViewController {
                                        rootVC.present(alert, animated: true, completion: nil)
                                    }
                                }
                                .font(.headline)
                                .padding()
                                .frame(width: 150, height: 50)
                                .background(Color.white)
                                .foregroundColor(.black)
                                .cornerRadius(10)
                                .shadow(color: Color.gray.opacity(0.5), radius: 5, x: 0, y: 5)
                                .padding(.bottom, 40)
                            }
                        }
                    }
                    .navigationTitle("Pebl")
                    .toolbar {
                        ToolbarItem(placement: .navigationBarTrailing) {
                            Menu {
                                Button(action: {
                                    settingsManager.toggleDarkMode()
                                }) {
                                    Label(
                                        settingsManager.settings.isDarkModeEnabled ? "Light Mode" : "Dark Mode",
                                        systemImage: settingsManager.settings.isDarkModeEnabled ? "sun.max.fill" : "moon.fill"
                                    )
                                }

                                Button(action: {
                                    showingSettings = true
                                }) {
                                    Label("Settings", systemImage: "gear")
                                }
                            } label: {
                                Image(systemName: "ellipsis.circle")
                                    .foregroundColor(.primary)
                            }
                        }
                    }
                }
            }
            .onAppear {
                DispatchQueue.main.async {
                    loadCategoriesFromFile()
                    // Perform automatic cleanup of expired messages if needed
                    categoryManager.performAutomaticCleanupIfNeeded(using: settingsManager)
                }
            }
            .preferredColorScheme(settingsManager.settings.isDarkModeEnabled ? .dark : .light)
            .sheet(isPresented: $showingSettings) {
                SettingsView(settingsManager: settingsManager)
            }
            }
        }
    }

    private func categorizeMessage(_ message: String) {
        guard !message.isEmpty else { return }

        inputMessage = "" // Clear input immediately for better UX

        // Use the new centralized categorization system with banner notifications
        categoryManager.addMessageWithDetails(message) { result in
            if result.success {
                self.categoryManager.saveToFile()
            } else {
                print("⚠️ Failed to categorize message: \(message)")
            }
        }
    }

    private func addNewCategory(_ newCategory: String) {
        guard !newCategory.isEmpty else { return }

        // Create category with default icon first for immediate UI feedback
        let category = categoryManager.addRootCategory(name: newCategory, sfSymbol: "folder.fill")
        categoryManager.saveToFile()

        // Get appropriate SF symbol in background
        DispatchQueue.global(qos: .userInitiated).async {
            let aiModel = AIModel()
            let sfSymbol = aiModel.getSFSymbolForCategory(newCategory)

            DispatchQueue.main.async {
                category.sfSymbol = sfSymbol
                self.categoryManager.saveToFile()
            }
        }
    }

    private func moveMessageToCategory(_ message: Message, targetCategory: Category) {
        // Find and remove the message from its current location
        var messageFound = false

        // Check all root categories and their subcategories
        for rootCategory in categoryManager.rootCategories {
            // Check root category messages
            if let index = rootCategory.messages.firstIndex(where: { $0.id == message.id }) {
                rootCategory.messages.remove(at: index)
                messageFound = true
                break
            }

            // Check subcategory messages
            for subcategory in rootCategory.subcategories {
                if let index = subcategory.messages.firstIndex(where: { $0.id == message.id }) {
                    subcategory.messages.remove(at: index)
                    messageFound = true
                    break
                }
            }

            if messageFound { break }
        }

        // Add the message to the target category with updated parsed content
        if messageFound {
            var updatedMessage = message
            updatedMessage.updateParsedContent(for: targetCategory.name)
            targetCategory.addMessage(updatedMessage)
            categoryManager.saveToFile()
        }
    }

    private func moveMessageToSubcategory(_ message: Message, targetSubcategory: Category) {
        // Find and remove the message from its current location
        var messageFound = false

        // Check all root categories and their subcategories
        for rootCategory in categoryManager.rootCategories {
            // Check root category messages
            if let index = rootCategory.messages.firstIndex(where: { $0.id == message.id }) {
                rootCategory.messages.remove(at: index)
                messageFound = true
                break
            }

            // Check subcategory messages
            for subcategory in rootCategory.subcategories {
                if let index = subcategory.messages.firstIndex(where: { $0.id == message.id }) {
                    subcategory.messages.remove(at: index)
                    messageFound = true
                    break
                }
            }

            if messageFound { break }
        }

        // Add the message to the target subcategory with updated parsed content
        if messageFound {
            var updatedMessage = message
            updatedMessage.updateParsedContent(for: targetSubcategory.name)
            targetSubcategory.addMessage(updatedMessage)
            categoryManager.saveToFile()
        }
    }

    // This function is now handled in CategoryManager.saveToFile()

    private func loadCategoriesFromFile() {
        let fileManager = FileManager.default
        if let documentDirectory = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first {
            let fileURL = documentDirectory.appendingPathComponent("categories.json")
            if fileManager.fileExists(atPath: fileURL.path) {
                do {
                    let data = try Data(contentsOf: fileURL)

                    // Try to decode as new format first
                    if let loadedCategories = try? JSONDecoder().decode([Category].self, from: data) {
                        categoryManager.rootCategories = loadedCategories
                        // Set categoryManager reference for all loaded categories
                        setCategoryManagerReferences(for: loadedCategories)
                        return
                    }

                    // If that fails, try to decode as old format and migrate
                    if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
                       let oldCategories = json["categories"] as? [String: [String]],
                       let oldCategoryImages = json["categoryImages"] as? [String: String] {

                        // Migrate old format to new format
                        categoryManager.rootCategories = []
                        for (categoryName, messageStrings) in oldCategories {
                            let sfSymbol = oldCategoryImages[categoryName] ?? "folder.fill"
                            let category = categoryManager.addRootCategory(name: categoryName, sfSymbol: sfSymbol)
                            for messageString in messageStrings {
                                category.addMessage(messageString)
                            }
                        }

                        // Save in new format
                        categoryManager.saveToFile()
                        print("Successfully migrated categories from old format to new format")
                        return
                    }

                    print("Could not decode categories file in any known format")

                } catch {
                    print("Error loading categories: \(error)")
                    // Keep default categories if loading fails
                }
            }
            // Default categories are already loaded in CategoryManager init
        }
    }

    /// Set categoryManager references for loaded categories (recursive)
    private func setCategoryManagerReferences(for categories: [Category]) {
        for category in categories {
            category.categoryManager = categoryManager
            setCategoryManagerReferences(for: category.subcategories)
        }
    }


}

// MARK: - Collapsible Subcategory View

struct CollapsibleSubcategoryView: View {
    @ObservedObject var subcategory: Category
    let onMessageDelete: (UUID) -> Void
    let onMessageMove: ((Message, Category) -> Void)?
    let onMessageLongPress: (Message) -> Void
    let onMessageEdit: (Message) -> Void

    @State private var isExpanded: Bool = false
    @State private var sortedMessages: [Message] = []

    var body: some View {
        VStack(spacing: 0) {
            // Header - always visible, tappable to expand/collapse
            Button(action: {
                withAnimation(.easeInOut(duration: 0.3)) {
                    isExpanded.toggle()
                }
            }) {
                HStack(spacing: 12) {
                    // Expand/collapse indicator
                    Image(systemName: isExpanded ? "chevron.down" : "chevron.right")
                        .font(.system(size: 14, weight: .semibold))
                        .foregroundColor(.secondary)
                        .frame(width: 16)

                    // Subcategory icon
                    Image(systemName: subcategory.sfSymbol.isEmpty ? "folder.fill" : subcategory.sfSymbol)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.primary)
                        .frame(width: 20)

                    // Subcategory name
                    Text(subcategory.name)
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.primary)
                        .multilineTextAlignment(.leading)

                    Spacer()

                    // Message count badge
                    if subcategory.getTotalMessageCount() > 0 {
                        Text("\(subcategory.getTotalMessageCount())")
                            .font(.system(size: 12, weight: .medium))
                            .foregroundColor(.secondary)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(Color(.systemGray5))
                            .cornerRadius(10)
                    }
                }
                .padding(.vertical, 12)
                .padding(.horizontal, 16)
                .background(Color(.systemGray6))
                .cornerRadius(8)
            }
            .buttonStyle(PlainButtonStyle())
            .dropDestination(for: Message.self) { messages, location in
                // Handle dropped messages
                for message in messages {
                    // Move message to this subcategory
                    onMessageMove?(message, subcategory)
                }
                return true
            }

            // Expanded content - messages
            if isExpanded && !sortedMessages.isEmpty {
                VStack(spacing: 8) {
                    VStack(spacing: 6) {
                        ForEach($sortedMessages) { $message in
                            ModernMessageView(
                                message: $message,
                                onToggle: {
                                    // Use the proper toggle method that triggers persistence
                                    subcategory.toggleMessageCompletion(withId: message.id)
                                    updateSortedMessages()
                                },
                                showTimestamp: true,
                                onMove: {
                                    onMessageLongPress(message)
                                },
                                onEdit: {
                                    onMessageEdit(message)
                                }
                            )
                            .contextMenu {
                                Button("Edit") {
                                    onMessageEdit(message)
                                }
                                Button("Move") {
                                    onMessageLongPress(message)
                                }
                                Button("Delete", role: .destructive) {
                                    onMessageDelete(message.id)
                                    updateSortedMessages()
                                }
                            }
                        }
                    }
                    .padding(.horizontal, 16)
                    .padding(.vertical, 8)


                }
                .background(Color(.systemBackground))
                .cornerRadius(8)
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(Color(.systemGray4), lineWidth: 0.5)
                )
                .padding(.top, 4)
            }

            // Show "No messages" when expanded but empty
            if isExpanded && sortedMessages.isEmpty {
                Text("No messages")
                    .font(.system(size: 14))
                    .foregroundColor(.secondary)
                    .padding(.vertical, 16)
                    .frame(maxWidth: .infinity)
                    .background(Color(.systemGray6))
                    .cornerRadius(8)
                    .padding(.top, 4)
            }
        }
        .onAppear {
            updateSortedMessages()
        }
        .onChange(of: subcategory.messages) { _ in
            updateSortedMessages()
        }
    }

    private func updateSortedMessages() {
        sortedMessages = subcategory.messages.sorted { first, second in
            if first.isCompleted != second.isCompleted {
                return !first.isCompleted // Active messages first
            }
            return first.timestamp > second.timestamp // Newer messages first within each group
        }
    }
}

// MARK: - Category Detail View

struct CategoryDetailView: View {
    @ObservedObject var category: Category
    @ObservedObject var categoryManager: CategoryManager
    let onBack: () -> Void
    let onNavigateToSubcategory: (Category) -> Void

    @State private var sortedMessages: [Message] = []
    @State private var showActionBar: Bool = UserDefaults.standard.bool(forKey: "showActionBar")
    @State private var showMoveSheet: Bool = false
    @State private var messageToMove: Message? = nil
    @State private var showEditSheet: Bool = false
    @State private var messageToEdit: Message? = nil

    var body: some View {
        ScrollView(.vertical, showsIndicators: true) {
            LazyVStack(spacing: 0) {
                // Messages in this category
                if !sortedMessages.isEmpty {
                    LazyVStack(spacing: 8) {
                        ForEach($sortedMessages) { $message in
                            ModernMessageView(
                                message: $message,
                                onToggle: {
                                    // Use the proper toggle method that triggers persistence
                                    category.toggleMessageCompletion(withId: message.id)
                                    updateSortedMessages()
                                },
                                showTimestamp: true,
                                onMove: {
                                    // Store the message to move and show move sheet
                                    messageToMove = message
                                    showMoveSheet = true
                                },
                                onEdit: {
                                    // Store the message to edit and show edit sheet
                                    messageToEdit = message
                                    showEditSheet = true
                                }
                            )
                            .contextMenu {
                                Button("Edit") {
                                    messageToEdit = message
                                    showEditSheet = true
                                }
                                Button("Move") {
                                    messageToMove = message
                                    showMoveSheet = true
                                }
                                Button("Delete", role: .destructive) {
                                    category.removeMessage(withId: message.id)
                                    updateSortedMessages()
                                }
                            }
                        }
                    }
                    .padding(.horizontal, 16)
                    .padding(.top, 8)



                    // Visual separator between main messages and subcategories (only if both exist)
                    if !sortedMessages.isEmpty && !category.subcategories.isEmpty {
                        Divider()
                            .padding(.horizontal, 16)
                            .padding(.vertical, 8)
                    }
                }

                // Subcategories as collapsible lists - directly below messages with no spacing
                if !category.subcategories.isEmpty {
                    LazyVStack(spacing: 0) {
                        ForEach(category.subcategories.sorted(by: { $0.name < $1.name }), id: \.id) { subcategory in
                            CollapsibleSubcategoryView(
                                subcategory: subcategory,
                                onMessageDelete: { messageId in
                                    subcategory.removeMessage(withId: messageId)
                                },
                                onMessageMove: { message, targetCategory in
                                    // This is for drag-and-drop functionality
                                    moveMessageToSubcategory(message, targetSubcategory: targetCategory)
                                },
                                onMessageLongPress: { message in
                                    messageToMove = message
                                    showMoveSheet = true
                                },
                                onMessageEdit: { message in
                                    messageToEdit = message
                                    showEditSheet = true
                                }
                            )
                        }
                    }
                    .padding(.horizontal, 16)
                }

                // Add bottom padding to ensure content doesn't get cut off and provide space for scrolling
                Spacer()
                    .frame(height: 80)
            }
        }
        .navigationTitle(category.name)
        .onAppear {
            updateSortedMessages()
            // Show action bar by default on first appearance if user hasn't collapsed it
            if UserDefaults.standard.object(forKey: "showActionBar") == nil {
                showActionBar = true
                UserDefaults.standard.set(true, forKey: "showActionBar")
            }
        }
        .onChange(of: category.messages) { _ in
            updateSortedMessages()
        }
        .sheet(isPresented: $showMoveSheet) {
            CategorySelectionSheet(
                categoryManager: categoryManager,
                currentCategory: category,
                onCategorySelected: { targetCategory in
                    if let messageToMove = messageToMove {
                        // Move single message (from long press)
                        moveSingleMessage(messageToMove, to: targetCategory)
                    } else {
                        // Move selected messages (from selection)
                        moveSelectedMessages(to: targetCategory)
                    }
                    showMoveSheet = false
                }
            )
        }
        .sheet(isPresented: $showEditSheet) {
            if let messageToEdit = messageToEdit {
                EditMessageSheet(
                    isPresented: $showEditSheet,
                    message: messageToEdit,
                    onSave: { newText in
                        category.editMessage(withId: messageToEdit.id, newText: newText)
                        updateSortedMessages()
                        categoryManager.saveToFile()
                    }
                )
            }
        }
        .overlay(
            // Bottom overlay for action bar
            VStack {
                Spacer()
                if hasAnySelectedMessages() && showActionBar {
                    BottomDeleteBar(
                        onDelete: clearAllSelected,
                        onCollapse: {
                            showActionBar = false
                            UserDefaults.standard.set(false, forKey: "showActionBar")
                        }
                    )
                    .transition(.move(edge: .bottom).combined(with: .opacity))
                    .animation(.easeInOut(duration: 0.3), value: showActionBar)
                }
            }
        )
        .overlay(
            // Side pull tab for restoring action bar
            HStack {
                Spacer()
                if hasAnySelectedMessages() && !showActionBar {
                    SidePullTab(
                        onExpand: {
                            showActionBar = true
                            UserDefaults.standard.set(true, forKey: "showActionBar")
                        }
                    )
                    .transition(.move(edge: .trailing).combined(with: .opacity))
                    .animation(.easeInOut(duration: 0.3), value: showActionBar)
                }
            }
        )
        .toolbar {
            ToolbarItem(placement: .navigationBarLeading) {
                Button("Back") {
                    onBack()
                }
                .foregroundColor(.black)
            }

            ToolbarItem(placement: .navigationBarTrailing) {
                Menu {
                    Button("Add Subcategory") {
                        showAddSubcategoryAlert()
                    }

                    Button("Delete Category", role: .destructive) {
                        showDeleteCategoryAlert()
                    }
                } label: {
                    Image(systemName: "ellipsis")
                        .foregroundColor(.black)
                        .font(.title2)
                }
            }
        }
    }

    private func showAddSubcategoryAlert() {
        let alert = UIAlertController(title: "New Subcategory", message: "Enter a name for the new subcategory", preferredStyle: .alert)
        alert.addTextField { textField in
            textField.placeholder = "Subcategory name"
        }
        alert.addAction(UIAlertAction(title: "Add", style: .default) { _ in
            if let subcategoryName = alert.textFields?.first?.text, !subcategoryName.isEmpty {
                // Use the new system that includes recategorization
                category.addSubcategoryWithRecategorization(name: subcategoryName, sfSymbol: "folder.fill") { result in
                    if result.success {
                        // Get appropriate SF symbol in background
                        DispatchQueue.global(qos: .userInitiated).async {
                            let aiModel = AIModel()
                            let sfSymbol = aiModel.getSFSymbolForCategory(subcategoryName)

                            DispatchQueue.main.async {
                                result.subcategory.sfSymbol = sfSymbol

                                // Show recategorization results if any messages were moved
                                if !result.recategorization.movedMessages.isEmpty {
                                    showRecategorizationResults(result.recategorization)
                                }
                            }
                        }
                    } else {
                        print("⚠️ Failed to add subcategory: \(subcategoryName)")
                    }
                }
            }
        })
        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel, handler: nil))

        if let scene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let window = scene.windows.first,
           let rootVC = window.rootViewController {
            rootVC.present(alert, animated: true, completion: nil)
        }
    }

    private func showDeleteCategoryAlert() {
        let alert = UIAlertController(title: "Delete Category", message: "Do you want to distribute the contents to other categories?", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "Yes", style: .default) { _ in
            DispatchQueue.main.async {
                let aiModel = AIModel()
                category.redistributeMessages(using: aiModel, to: categoryManager.rootCategories)

                // Remove this category from its parent or root
                if let parent = category.parent {
                    parent.removeSubcategory(category)
                } else {
                    categoryManager.removeRootCategory(category)
                }
                onBack()
            }
        })
        alert.addAction(UIAlertAction(title: "No, delete all", style: .destructive) { _ in
            if let parent = category.parent {
                parent.removeSubcategory(category)
            } else {
                categoryManager.removeRootCategory(category)
            }
            onBack()
        })
        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel, handler: nil))

        if let scene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let window = scene.windows.first,
           let rootVC = window.rootViewController {
            rootVC.present(alert, animated: true, completion: nil)
        }
    }

    /// Show recategorization results to the user
    private func showRecategorizationResults(_ result: RecategorizationResult) {
        let movedCount = result.movedMessages.count
        let newCategoriesCount = result.newCategoriesCreated.count

        var message = "Recategorization completed!\n"
        message += "• \(movedCount) messages moved to better categories\n"
        message += "• \(result.skippedCount) messages stayed in place\n"

        if newCategoriesCount > 0 {
            message += "• \(newCategoriesCount) new categories created: \(result.newCategoriesCreated.joined(separator: ", "))"
        }

        let alert = UIAlertController(title: "Smart Recategorization", message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "OK", style: .default, handler: nil))

        if let scene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let window = scene.windows.first,
           let rootVC = window.rootViewController {
            rootVC.present(alert, animated: true, completion: nil)
        }
    }

    // MARK: - Helper Methods

    private func updateSortedMessages() {
        sortedMessages = category.messages.sorted { $0.timestamp > $1.timestamp }
    }

    /// Check if there are any selected messages in the main category or any subcategories
    private func hasAnySelectedMessages() -> Bool {
        // Check main category messages
        if category.messages.contains(where: { $0.isCompleted }) {
            return true
        }

        // Check all subcategory messages
        for subcategory in category.subcategories {
            if subcategory.messages.contains(where: { $0.isCompleted }) {
                return true
            }
        }

        return false
    }

    /// Clear all selected messages from the main category and all subcategories
    private func clearAllSelected() {
        // Clear selected messages from main category
        category.removeCompletedMessages()

        // Clear selected messages from all subcategories
        for subcategory in category.subcategories {
            subcategory.removeCompletedMessages()
        }

        // Update the sorted messages display
        updateSortedMessages()

        // Save changes to file
        categoryManager.saveToFile()
    }

    /// Move all selected messages to the target category
    private func moveSelectedMessages(to targetCategory: Category) {
        // Collect all selected messages from main category
        let selectedMainMessages = category.messages.filter { $0.isCompleted }

        // Collect all selected messages from subcategories
        var selectedSubcategoryMessages: [(message: Message, sourceCategory: Category)] = []
        for subcategory in category.subcategories {
            let selectedMessages = subcategory.messages.filter { $0.isCompleted }
            for message in selectedMessages {
                selectedSubcategoryMessages.append((message: message, sourceCategory: subcategory))
            }
        }

        // Move messages from main category
        for message in selectedMainMessages {
            var updatedMessage = message
            updatedMessage.isCompleted = false // Reset completion status
            updatedMessage.updateParsedContent(for: targetCategory.name)

            // Add to target category
            targetCategory.addMessage(updatedMessage)

            // Remove from source category
            category.removeMessage(withId: message.id)
        }

        // Move messages from subcategories
        for (message, sourceCategory) in selectedSubcategoryMessages {
            var updatedMessage = message
            updatedMessage.isCompleted = false // Reset completion status
            updatedMessage.updateParsedContent(for: targetCategory.name)

            // Add to target category
            targetCategory.addMessage(updatedMessage)

            // Remove from source subcategory
            sourceCategory.removeMessage(withId: message.id)
        }

        // Update the sorted messages display
        updateSortedMessages()

        // Save changes to file
        categoryManager.saveToFile()
    }

    /// Move a single message to the target category (for long press move)
    private func moveSingleMessage(_ message: Message, to targetCategory: Category) {
        var updatedMessage = message
        updatedMessage.isCompleted = false // Reset completion status
        updatedMessage.updateParsedContent(for: targetCategory.name)

        // Add to target category
        targetCategory.addMessage(updatedMessage)

        // Find and remove from source category (could be main category or subcategory)
        if category.messages.contains(where: { $0.id == message.id }) {
            category.removeMessage(withId: message.id)
        } else {
            // Check subcategories
            for subcategory in category.subcategories {
                if subcategory.messages.contains(where: { $0.id == message.id }) {
                    subcategory.removeMessage(withId: message.id)
                    break
                }
            }
        }

        // Update the sorted messages display
        updateSortedMessages()

        // Save changes to file
        categoryManager.saveToFile()

        // Clear the message to move
        messageToMove = nil
    }

    /// Move a message to a subcategory (for drag and drop)
    private func moveMessageToSubcategory(_ message: Message, targetSubcategory: Category) {
        // Find and remove the message from its current location
        var messageFound = false

        // Check main category messages
        if let index = category.messages.firstIndex(where: { $0.id == message.id }) {
            category.messages.remove(at: index)
            messageFound = true
        } else {
            // Check subcategory messages
            for subcategory in category.subcategories {
                if let index = subcategory.messages.firstIndex(where: { $0.id == message.id }) {
                    subcategory.messages.remove(at: index)
                    messageFound = true
                    break
                }
            }
        }

        // Add the message to the target subcategory with updated parsed content
        if messageFound {
            var updatedMessage = message
            updatedMessage.updateParsedContent(for: targetSubcategory.name)
            targetSubcategory.addMessage(updatedMessage)
            updateSortedMessages()
            categoryManager.saveToFile()
        }
    }
}

// MARK: - Bottom Action Bar

struct BottomDeleteBar: View {
    let onDelete: () -> Void
    let onCollapse: () -> Void

    var body: some View {
        HStack(spacing: 16) {
            // Delete button (red with text)
            Button(action: onDelete) {
                HStack(spacing: 8) {
                    Image(systemName: "trash")
                        .font(.system(size: 16, weight: .medium))
                    Text("Delete")
                        .font(.system(size: 16, weight: .medium))
                }
                .foregroundColor(.white)
                .padding(.horizontal, 20)
                .padding(.vertical, 12)
                .background(Color.red)
                .clipShape(Capsule())
            }

            Spacer()

            // Collapse button
            Button(action: onCollapse) {
                Image(systemName: "chevron.down")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.secondary)
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: -2)
        )
        .padding(.horizontal, 16)
        .padding(.bottom, 8)
    }
}

// MARK: - Side Pull Tab

struct SidePullTab: View {
    let onExpand: () -> Void

    var body: some View {
        VStack {
            Spacer()

            Button(action: onExpand) {
                HStack(spacing: 4) {
                    Image(systemName: "chevron.left")
                        .font(.system(size: 12, weight: .medium))
                    Image(systemName: "ellipsis")
                        .font(.system(size: 12))
                }
                .foregroundColor(.white)
                .padding(.horizontal, 8)
                .padding(.vertical, 12)
                .background(
                    LinearGradient(
                        gradient: Gradient(colors: [Color.blue, Color.blue.opacity(0.8)]),
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .clipShape(
                    RoundedRectangle(cornerRadius: 16)
                        .offset(x: 8) // Extend beyond the screen edge
                )
                .shadow(color: Color.blue.opacity(0.3), radius: 4, x: -2, y: 0)
            }
            .offset(x: 8) // Position partially off-screen

            Spacer()
        }
        .padding(.bottom, 100) // Position above bottom safe area
    }
}

// MARK: - Category Selection Sheet

struct CategorySelectionSheet: View {
    @ObservedObject var categoryManager: CategoryManager
    let currentCategory: Category
    let onCategorySelected: (Category) -> Void
    @Environment(\.dismiss) private var dismiss

    @State private var showingNewCategoryAlert = false
    @State private var newCategoryName = ""

    var body: some View {
        NavigationView {
            List {
                // Create New Category option
                Section {
                    Button(action: {
                        showingNewCategoryAlert = true
                    }) {
                        HStack {
                            Image(systemName: "plus.circle.fill")
                                .foregroundColor(.blue)
                                .frame(width: 20)

                            Text("Create New Category")
                                .foregroundColor(.blue)

                            Spacer()
                        }
                    }
                }

                // Existing categories
                Section("Existing Categories") {
                    ForEach(categoryManager.rootCategories, id: \.id) { rootCategory in
                        CategorySelectionRow(
                            category: rootCategory,
                            currentCategory: currentCategory,
                            onCategorySelected: onCategorySelected
                        )
                    }
                }
            }
            .navigationTitle("Move to Category")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
            .alert("New Category", isPresented: $showingNewCategoryAlert) {
                TextField("Category name", text: $newCategoryName)
                Button("Create") {
                    createNewCategory()
                }
                .disabled(newCategoryName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
                Button("Cancel", role: .cancel) {
                    newCategoryName = ""
                }
            } message: {
                Text("Enter a name for the new category")
            }
        }
    }

    private func createNewCategory() {
        let trimmedName = newCategoryName.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedName.isEmpty else { return }

        // Create category with default icon first for immediate UI feedback
        let category = categoryManager.addRootCategory(name: trimmedName, sfSymbol: "folder.fill")
        categoryManager.saveToFile()

        // Get appropriate SF symbol in background
        DispatchQueue.global(qos: .userInitiated).async {
            let aiModel = AIModel()
            let sfSymbol = aiModel.getSFSymbolForCategory(trimmedName)

            DispatchQueue.main.async {
                category.sfSymbol = sfSymbol
                categoryManager.saveToFile()

                // Select the newly created category
                onCategorySelected(category)
                newCategoryName = ""
            }
        }
    }
}

struct CategorySelectionRow: View {
    let category: Category
    let currentCategory: Category
    let onCategorySelected: (Category) -> Void
    @State private var isExpanded = false

    var isCurrentCategory: Bool {
        category.id == currentCategory.id
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            // Main category row
            HStack {
                Image(systemName: category.sfSymbol)
                    .foregroundColor(.blue)
                    .frame(width: 20)

                Text(category.name)
                    .foregroundColor(isCurrentCategory ? .secondary : .primary)

                Spacer()

                if !category.subcategories.isEmpty {
                    Button(action: {
                        withAnimation(.easeInOut(duration: 0.2)) {
                            isExpanded.toggle()
                        }
                    }) {
                        Image(systemName: isExpanded ? "chevron.down" : "chevron.right")
                            .foregroundColor(.secondary)
                            .font(.system(size: 12, weight: .medium))
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
            .contentShape(Rectangle())
            .onTapGesture {
                if !isCurrentCategory {
                    onCategorySelected(category)
                }
            }
            .disabled(isCurrentCategory)

            // Subcategories
            if isExpanded && !category.subcategories.isEmpty {
                VStack(alignment: .leading, spacing: 4) {
                    ForEach(category.subcategories.sorted(by: { $0.name < $1.name }), id: \.id) { subcategory in
                        HStack {
                            Spacer()
                                .frame(width: 32) // Indent for subcategory

                            Image(systemName: subcategory.sfSymbol)
                                .foregroundColor(.blue)
                                .frame(width: 20)

                            Text(subcategory.name)
                                .foregroundColor(subcategory.id == currentCategory.id ? .secondary : .primary)

                            Spacer()
                        }
                        .contentShape(Rectangle())
                        .onTapGesture {
                            if subcategory.id != currentCategory.id {
                                onCategorySelected(subcategory)
                            }
                        }
                        .disabled(subcategory.id == currentCategory.id)
                    }
                }
                .padding(.top, 8)
            }
        }
        .padding(.vertical, 4)
    }
}

#Preview {
    ContentView()
}
